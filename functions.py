import json
import glob
import shutil
import subprocess
import urllib

import requests
import utils
from config import HTML_DIR, CODE_DIR, DATA_DIR, GENERATE_MODEL_NAME, BROWSER_EXECUTABLE_PATH, message_history
from utils import extract_code, client
from pathlib import Path
from playwright.sync_api import (
    sync_playwright, TimeoutError as PwTimeoutError, Browser, Page
)
from typing import Callable  # 顶部加这句

import os
import re
import time
import logging
from openai import OpenAI
from config import trip_baseUrl, trip_apikey, logger
from flask import request

try:
    from flask_socketio import emit
except ImportError:
    emit = None
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 预定义过滤规则
KEEP_TAGS = {
    'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'a', 'li', 'td', 'th', 'label', 'button', 'input', 'textarea',
    'article', 'section', 'main', 'header', 'footer', 'nav'
}

FILTER_TAGS = {
    'script', 'style', 'meta', 'link', 'noscript', 'iframe',
    'svg', 'path', 'img', 'br', 'hr', 'col', 'colgroup'
}

FILTER_CLASSES = {
    'hidden', 'invisible', 'collapse', 'none',
    'ad-', 'ads-', 'banner', 'popup', 'modal',
    'cookie', 'privacy', 'footer', 'header', 'nav',
    'menu', 'sidebar', 'widget', 'comment'
}

FILTER_IDS = {
    'ad-', 'ads-', 'banner', 'popup', 'modal',
    'cookie', 'privacy', 'footer', 'header', 'nav',
    'menu', 'sidebar', 'widget', 'comment'
}

# 编译正则表达式
TAG_PATTERN = re.compile(r'<([a-zA-Z0-9-]+)([^>]*)>(.*?)</\1>', re.DOTALL)
CLASS_PATTERN = re.compile(r'class=["\']([^"\']*)["\']')
ID_PATTERN = re.compile(r'id=["\']([^"\']*)["\']')
ROLE_PATTERN = re.compile(r'role=["\']([^"\']*)["\']')
TEXT_PATTERN = re.compile(r'>([^<>]+)<')
HISTORY_FILE = os.path.join(os.path.dirname(__file__), 'history.json')


def save_history_record(prompt, code=None, result=None, file_path=None, code_file_path=None, status=None):
    record = {
        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'prompt': prompt,
        'code': code,
        'result': result,
        'file_path': file_path,
        'code_file_path': code_file_path,
        'status': status or '已完成'
    }
    if os.path.exists(HISTORY_FILE):
        with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
            history = json.load(f)
    else:
        history = []

    # 定义任务唯一性判断
    def is_same_task(r):
        return (
                r.get('prompt', {}).get('url') == prompt.get('url') and
                r.get('prompt', {}).get('type') == prompt.get('type') and
                r.get('prompt', {}).get('requirements') == prompt.get('requirements')
        )

    found = False
    for i, r in enumerate(history):
        if is_same_task(r):
            found = True
            if r.get('status') == '已完成':
                # 已有成功记录，不再保存
                return
            elif r.get('status') == '失败' and record['status'] == '已完成':
                # 覆盖失败记录为成功
                history[i] = record
                with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
                    json.dump(history, f, ensure_ascii=False, indent=2)
                return
            else:
                # 已有失败记录且本次也是失败，不保存
                return
    if not found:
        history.append(record)
        with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)


def open_and_save_page(
        url: str,
        save_name: str,
        headless: bool = True,
        *,
        # ↓ 以下都已给出“通用”默认值，通常无需关心 ↓
        browser_exe: str | None = BROWSER_EXECUTABLE_PATH,
        global_timeout: int = 45_000,  # 整体硬超时 45 s
        quiet_ms: int = 800,  # networkidle 静默窗口
        wait_selectors: tuple[str, ...] = (),  # 关键 CSS
        wait_responses: tuple[str | re.Pattern | Callable, ...] = (),
        wait_funcs: tuple[str, ...] = (),  # JS 条件
        auto_scroll: bool = True,
        scroll_gap_px: int = 800,
        max_scroll_rounds: int = 15,
        save_screenshot: bool = False,
) -> dict:
    """
    通用：打开 URL → 智能等待 → (可选)滚动 → 保存 HTML（+可选截图）

    仅需 url、save_name、headless 三参即可快速使用。
    其他参数在极端页面再手动覆写。
    """
    # sid = getattr(request, 'sid', None)
    # if emit and sid:
    #     emit('progress_update', {'step': 'open_and_save_page', 'message': '正在使用本地浏览器访问站点...'}, to=sid)
    save_path = Path(HTML_DIR) / save_name

    def _adapt(matcher):
        if callable(matcher):
            return matcher
        if isinstance(matcher, re.Pattern):
            return lambda r, m=matcher: m.search(r.url) and r.ok
        pattern = re.compile(matcher)
        return lambda r, m=pattern: m.search(r.url) and r.ok

    with sync_playwright() as p:
        browser: Browser = p.chromium.launch(
            headless=headless, executable_path=browser_exe
        )
        page: Page = browser.new_page()
        try:
            page.goto(url, wait_until="domcontentloaded", timeout=global_timeout)
            try:
                page.wait_for_load_state("networkidle",
                                         timeout=global_timeout)
            except PwTimeoutError:
                pass  # 若一直轮询，忽略

            for css in wait_selectors:
                page.wait_for_selector(css, state="visible", timeout=global_timeout)

            for cond in wait_responses:
                page.wait_for_response(_adapt(cond), timeout=global_timeout)

            for js in wait_funcs:
                page.wait_for_function(js, timeout=global_timeout)

            if auto_scroll:
                _auto_scroll(page, scroll_gap_px, max_scroll_rounds)

        except PwTimeoutError:
            print("[Warn] 等待超时，已落盘当前 DOM。")
        finally:
            save_path.parent.mkdir(parents=True, exist_ok=True)
            save_path.write_text(page.content(), encoding="utf-8")
            if save_screenshot:
                page.screenshot(path=str(save_path.with_suffix(".png")),
                                full_page=True)
            browser.close()
    print(f"页面 HTML 已保存至 {save_path}")
    return {"message": f"页面 HTML 已保存至 {save_path}", "save_path": save_path}


# ——内部工具：自动滚动——
def _auto_scroll(page: Page, gap_px: int, max_rounds: int):
    last = page.evaluate("() => document.body.scrollHeight")
    for _ in range(max_rounds):
        page.evaluate(f"window.scrollBy(0,{gap_px});")
        page.wait_for_timeout(400)
        new = page.evaluate("() => document.body.scrollHeight")
        if new <= last:
            break
        last = new


def open_and_save_page_with_remote_browser(url, save_name):
    sid = getattr(request, 'sid', None)
    if emit and sid:
        emit('progress_update',
             {'step': 'open_and_save_page_with_remote_browser', 'message': '正在使用远程浏览器访问站点...'}, to=sid)
    """
    使用 远程浏览器 打开指定 URL，并将页面 HTML 保存到指定文件。
    """
    print("调用了open_and_save_page_with_remote_browser")
    print(f"参数: url={url}, save_name={save_name}")

    save_path = os.path.join(HTML_DIR, save_name)
    if not os.path.exists(HTML_DIR):
        os.makedirs(HTML_DIR)

    try:
        data = {"params": [{"type": "new-tab", "value": url}]}
        response = requests.post('http://47.117.68.255:21239/getWebContentExtension', data=json.dumps(data))

        if response.status_code == 200:
            html_txt = response.text
            json_data = json.loads(html_txt)

            # 检查json_data的结构
            if not isinstance(json_data, dict) or 'data' not in json_data:
                raise ValueError(f"无效的响应格式: {json_data}")

            # 解析data字段
            try:
                data_content = json.loads(json_data["data"])
                if not isinstance(data_content, dict) or 'content' not in data_content:
                    raise ValueError(f"无效的数据格式: {data_content}")

                html_content = data_content["content"]
            except (KeyError, json.JSONDecodeError) as e:
                raise ValueError(f"解析响应数据失败: {str(e)}, 原始数据: {json_data['data']}")

            # 保存HTML内容
            with open(save_path, "w", encoding="utf-8") as f:
                f.write(html_content)

            return {"message": f"页面 HTML 已保存至 {save_path}", "save_path": save_path}
        else:
            raise ValueError(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")

    except Exception as e:
        error_msg = f"访问远程浏览器服务出错: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)


def edit_code(new_code_path, new_python_code):
    sid = getattr(request, 'sid', None)
    if emit and sid:
        emit('progress_update', {'step': 'edit_code', 'message': '正在保存/编辑代码文件...'}, to=sid)
    save_path = os.path.join(CODE_DIR, new_code_path)
    with open(save_path, "w", encoding="utf-8") as f:
        f.write(new_python_code)
    return {"message": f"代码更新成功, 更新到 {save_path}"}


def get_playwright_code(
        clean_html_path_list,
        model_response,
        code_file_path,
        fail_messages=None
):
    """
    根据多个 HTML 文件和大模型响应生成 Playwright 代码
    —— 函数签名、返回字段与旧版一致，可直接替换 ——
    """
    # ---------- 推送进度 ----------
    sid = getattr(request, "sid", None)
    if emit and sid:
        emit("progress_update",
             {"step": "get_playwright_code", "message": "正在生成 Python 爬虫代码…"},
             to=sid)

    try:
        # ---------- 1. 代码保存路径 ----------
        save_path = os.path.join(CODE_DIR, code_file_path)
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # ---------- 2. 读取 HTML ----------
        html_contents = []
        for p in [i.strip() for i in clean_html_path_list.split(",") if i.strip()]:
            full = p if os.path.isabs(p) else os.path.join(HTML_DIR, p)
            if not os.path.exists(full):
                raise FileNotFoundError(f"找不到 HTML：{full}")
            html_contents.append({"path": full, "content": open(full, encoding="utf-8").read()})

        # ---------- 3. 最近上下文 ----------
        ctx_prompt = "\n".join(
            f"{m['role']}：{m.get('content', '')}" if m["role"] != "function"
            else f"函数 {m['name']} 结果：{m['content']}"
            for m in message_history.get(sid, [])[-5:]
        )

        # ---------- 4. 通用提示词（已修正 wait_for_function 参数写法） ----------
        runtime_snippet = utils.read_file_text("local_fake_browser.py")
        play_instruction = (
            "你是一名经验丰富且追求高效的 Playwright Python 工程师，只输出 **```python …```** 代码块：\n"
            "【导航多层等待】先 domcontentloaded → wait_for_selector → 可选 3 s networkidle 兜底。\n"
            "【极速优化】\n"
            "1. `context.route(\"**/*.{png,jpg,jpeg,webp,svg,woff,woff2}\", lambda r: r.abort())` 阻断大资源；\n"
            "2. 懒加载滚动：`scrollBy(0, 200)` + `page.wait_for_timeout(300)` 直到 scrollHeight 一次不变；\n"
            "3. 列表解析一次性放在 `page.evaluate()` 返回数组。\n"
            "【翻页强制】\n"
            "· 翻页前记录首条文本 `prev_text`；\n"
            "· 点击下一页；\n"
            "· `old_locator.wait_for(state='hidden', timeout=15000)`；\n"
            "· `page.wait_for_function(js_expr, arg=prev_text, timeout=15000, polling=250)` ——**必须用关键字参数**；\n"
            "· 再 `wait_for_selector('<main-selector>', timeout=15000)`。\n"
            "【登录/Cookie】如需展示 `context.add_cookies([...])`。\n"
            "【多页面/多请求优化】\n"
            "· 复用浏览器实例：使用同一个 `browser` 对象打开多个页面；\n"
            "· 每次成功请求立即保存数据：在每个页面处理完成后立即写入文件，避免数据丢失；\n"
            "· 异常处理：使用 try-except 包裹每个页面请求，确保一个页面失败不影响其他页面；\n"
            "· 连接池管理：合理设置 `browser.new_page()` 和 `page.close()`，避免页面过多导致内存溢出。\n"
            f"【启动器】必须调用以下本地内核启动器，不改内部参数：\n{runtime_snippet}\n"
            f"【文件路径】HTML→{HTML_DIR}；数据文件→{DATA_DIR}。\n"
            "【结尾强制】写入数据后打印 `print(f\"写入 {{len(events)}} 条数据到: {{save_to_data}}\")`。\n"
            "【关键要求】始终复用浏览器实例，不要在循环中重复启动/关闭浏览器，确保每次成功获取数据后立即保存。\n"
        )

        special_instruction = (
            "必须使用 Playwright；生成完整、可运行的 Python 代码；"
            "代码块必须放在 ```python``` 中；禁止输出除代码外的任何文字。"
        )

        # ---------- 5. 组装 LLM 消息 ----------
        msgs = [
            {"role": "system", "content": "你是一名 Playwright 爬虫专家"},
            {"role": "user", "content": ctx_prompt},
            {"role": "user", "content": model_response},
            {"role": "user", "content":
                "以下是多个 HTML 文件内容：\n" +
                "\n".join(f"<{h['path']}>\n{h['content']}" for h in html_contents)},
            {"role": "user", "content": play_instruction},
            {"role": "user", "content": special_instruction},
        ]
        if fail_messages:
            msgs.append({"role": "user", "content": f"上次错误日志：\n{fail_messages}"})

        # ---------- 6. 调用 LLM（最多 3 次指数退避） ----------
        max_retry, backoff = 3, 2
        python_code = ""
        for i in range(1, max_retry + 1):
            try:
                resp = client.chat.completions.create(
                    model=GENERATE_MODEL_NAME, messages=msgs
                ).choices[0].message.content
                python_code = extract_code(resp)
                if python_code.strip():
                    break
                raise ValueError("提取到的代码为空")
            except Exception as e:
                if i == max_retry:
                    raise
                if emit and sid:
                    emit("code_retry",
                         {"retry_count": i, "max_retries": max_retry, "reason": str(e)}, to=sid)
                time.sleep(backoff)
                backoff *= 2
                msgs.append({"role": "user", "content": f"上次失败：{e}，请重新生成完整代码"})

        # ---------- 7. 保存并返回 ----------
        with open(save_path, "w", encoding="utf-8") as f:
            f.write(python_code)
        return {
            "message": f"代码生成成功，已保存至 {save_path}",
            "save_path": save_path,
            "python_code": python_code,
            "html_files": [h["path"] for h in html_contents],
            "context": ctx_prompt
        }

    except Exception as e:
        err_msg = f"生成 Playwright 代码失败：{e}"
        print(err_msg)
        raise Exception(err_msg)


def execute_playwright_code(code_file_path):
    sid = getattr(request, 'sid', None)
    if emit and sid:
        emit('progress_update', {'step': 'execute_playwright_code', 'message': '正在执行python爬虫代码...'}, to=sid)
    try:
        # 处理代码文件路径
        if os.path.isabs(code_file_path):
            # 如果是绝对路径，检查文件是否存在
            if os.path.exists(code_file_path):
                code_path = code_file_path
            else:
                # 如果绝对路径不存在，尝试从CODE_DIR加载同名文件
                file_name = os.path.basename(code_file_path)
                code_path = os.path.join(CODE_DIR, file_name)
        else:
            # 相对路径，从CODE_DIR加载
            code_path = os.path.join(CODE_DIR, code_file_path)

        if not os.path.exists(code_path):
            raise FileNotFoundError(f"找不到代码文件: {code_path}")

        with open(code_path, "r", encoding="utf-8") as f:
            code = f.read()

        # 使用subprocess执行Python代码
        process = subprocess.Popen(
            ["python", code_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding='utf-8'
        )
        stdout, stderr = process.communicate()

        if process.returncode == 0:
            # 从代码中提取保存位置信息
            save_location = None
            if 'save_to' in code:
                save_match = re.search(r'save_to\s*=\s*[\'\"]([^\'\"]+)[\'\"]', code)
                if save_match:
                    save_location = os.path.join(DATA_DIR, save_match.group(1))
            elif 'output_file' in code:
                save_match = re.search(r'output_file\s*=\s*[\'\"]([^\'\"]+)[\'\"]', code)
                if save_match:
                    save_location = os.path.join(DATA_DIR, save_match.group(1))
            # 新增：从stdout中提取
            if not save_location and stdout:
                match = re.search(r'写入\s*\d+\s*条.*到[:：]\s*(.+)', stdout)
                if match:
                    save_location = match.group(1).strip()
            # 构建详细的结果消息
            result_message = f"""
执行代码文件：{code_file_path}
执行结果：成功
输出内容：
{stdout}
数据保存位置：{save_location or "未指定保存位置"}
"""

            return {
                "message": result_message,
                "output": stdout,
                "save_location": save_location or "未指定保存位置",
                "save_path": code_path,
                "status": True
            }
        else:
            error_message = f"""
执行代码文件：{code_file_path}
执行结果：失败
错误信息：
{stderr}
"""
            return {
                "message": error_message,
                "save_location": "执行失败",
                "save_path": code_path,
                "status": False
            }

    except Exception as e:
        error_message = f"""
执行代码文件：{code_file_path}
执行结果：失败
错误信息：{str(e)}
"""
        return {
            "message": error_message,
            "save_location": "执行失败",
            "save_path": code_file_path,
            "status": False
        }


def list_directory_contents(directory_path, recursive=True):
    sid = getattr(request, 'sid', None)
    if emit and sid:
        emit('progress_update', {'step': 'list_directory_contents', 'message': '正在检索目录内容...'}, to=sid)
    """
    检索指定目录下的所有文件

    Args:
        directory_path (str): 要检索的目录路径
        recursive (bool): 是否递归检索子目录

    Returns:
        dict: 包含文件信息的字典
    """
    try:
        # 确保目录路径存在
        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"目录不存在: {directory_path}")

        # 初始化结果字典
        result = {
            "directory": directory_path,
            "files": [],
            "directories": []
        }

        # 遍历目录
        for root, dirs, files in os.walk(directory_path):
            # 获取相对路径
            rel_path = os.path.relpath(root, directory_path)

            # 添加文件信息
            for file in files:
                file_path = os.path.join(root, file)
                file_info = {
                    "name": file,
                    "path": os.path.join(rel_path, file),
                    "full_path": file_path,
                    "size": os.path.getsize(file_path),
                    "modified_time": time.strftime('%Y-%m-%d %H:%M:%S',
                                                   time.localtime(os.path.getmtime(file_path)))
                }
                result["files"].append(file_info)

            # 添加子目录信息
            if recursive:
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    dir_info = {
                        "name": dir_name,
                        "path": os.path.join(rel_path, dir_name),
                        "full_path": dir_path
                    }
                    result["directories"].append(dir_info)

            # 如果不是递归模式，只处理顶层目录
            if not recursive:
                break

        return result

    except Exception as e:
        raise Exception(f"检索目录失败: {str(e)}")


def read_file_content(file_path, encoding='utf-8'):
    sid = getattr(request, 'sid', None)
    if emit and sid:
        emit('progress_update', {'step': 'read_file_content', 'message': '正在读取文件内容...'}, to=sid)
    """
    读取文件内容

    Args:
        file_path (str): 文件路径
        encoding (str): 文件编码，默认为 utf-8

    Returns:
        dict: 包含文件信息的字典
    """
    try:
        # 确保文件路径存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 获取文件信息
        file_info = {
            "name": os.path.basename(file_path),
            "path": file_path,
            "size": os.path.getsize(file_path),
            "modified_time": time.strftime('%Y-%m-%d %H:%M:%S',
                                           time.localtime(os.path.getmtime(file_path))),
            "content": None,
            "message": None
        }

        # 尝试读取文件内容
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                # 读取整个文件内容但只保留合理长度用于返回
                full_content = f.read()
                content_size = len(full_content)
                # 限制返回到消息历史中的内容大小，最多2000个字符
                max_content_size = 2000
                if content_size > max_content_size:
                    file_info["content"] = full_content[
                                           :max_content_size] + f"\n\n... [截断，完整文件大小为 {content_size} 字符] ..."
                else:
                    file_info["content"] = full_content

                # 优化返回消息，明确提示下一步操作
                preview_size = min(300, len(file_info["content"]))
                file_info[
                    "message"] = f"文件读取成功 ({file_info['size']} 字节)。文件内容预览:\n{file_info['content'][:preview_size]}\n\n[注意: 这只是文件内容的一部分，请继续执行必要的操作，如需查看其他内容或处理该文件，请使用function_call]"
        except UnicodeDecodeError:
            # 如果 UTF-8 解码失败，尝试其他编码
            encodings = ['gbk', 'gb2312', 'gb18030', 'big5']
            for enc in encodings:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        # 读取整个文件内容但只保留合理长度用于返回
                        full_content = f.read()
                        content_size = len(full_content)
                        # 限制返回到消息历史中的内容大小
                        max_content_size = 2000
                        if content_size > max_content_size:
                            file_info["content"] = full_content[
                                                   :max_content_size] + f"\n\n... [截断，完整文件大小为 {content_size} 字符] ..."
                        else:
                            file_info["content"] = full_content

                        # 优化返回消息，明确提示下一步操作
                        preview_size = min(300, len(file_info["content"]))
                        file_info[
                            "message"] = f"文件读取成功 ({file_info['size']} 字节，编码: {enc})。文件内容预览:\n{file_info['content'][:preview_size]}\n\n[注意: 这只是文件内容的一部分，请继续执行必要的操作，如需查看其他内容或处理该文件，请使用function_call]"
                        file_info["encoding"] = enc
                        break
                except UnicodeDecodeError:
                    continue

            if file_info["content"] is None:
                raise Exception(f"无法使用支持的编码读取文件: {file_path}")

        return file_info

    except Exception as e:
        raise Exception(f"读取文件失败: {str(e)}")


def check_file_exists(file_path):
    sid = getattr(request, 'sid', None)
    if emit and sid:
        emit('progress_update', {'step': 'check_file_exists', 'message': '正在检查文件是否存在...'}, to=sid)
    print(f"[DEBUG] check_file_exists received: {file_path} (type: {type(file_path)})")
    if not isinstance(file_path, str):
        file_path = str(file_path)
    file_path = os.path.normpath(file_path)
    """
    检查指定路径的文件是否存在

    Args:
        file_path (str): 要检查的文件路径

    Returns:
        dict: 包含文件存在状态和路径信息的字典
    """
    try:
        # 如果是相对路径，尝试在不同目录中查找
        if not os.path.isabs(file_path):
            # 检查在 HTML_DIR 中是否存在
            html_path = os.path.join(HTML_DIR, file_path)
            if os.path.exists(html_path):
                return {
                    "exists": True,
                    "message": f"文件存在于 HTML 目录: {html_path}",
                    "full_path": html_path
                }

            # 检查在 CODE_DIR 中是否存在
            code_path = os.path.join(CODE_DIR, file_path)
            if os.path.exists(code_path):
                return {
                    "exists": True,
                    "message": f"文件存在于代码目录: {code_path}",
                    "full_path": code_path
                }

            # 检查在 DATA_DIR 中是否存在
            data_path = os.path.join(DATA_DIR, file_path)
            if os.path.exists(data_path):
                return {
                    "exists": True,
                    "message": f"文件存在于数据目录: {data_path}",
                    "full_path": data_path
                }

            return {
                "exists": False,
                "message": f"文件不存在: {file_path}",
                "full_path": None
            }
        else:
            # 如果是绝对路径，直接检查
            exists = os.path.exists(file_path)
            return {
                "exists": exists,
                "message": f"文件{'存在' if exists else '不存在'}: {file_path}",
                "full_path": file_path if exists else None
            }

    except Exception as e:
        raise Exception(f"检查文件是否存在时出错: {str(e)}")


def delete_files(path_pattern, directory="all", recursive=False):
    sid = getattr(request, 'sid', None)
    if emit and sid:
        emit('progress_update', {'step': 'delete_files', 'message': '正在删除文件或目录...'}, to=sid)
    """
    删除指定路径的文件或目录，支持模糊匹配

    Args:
        path_pattern (str): 要删除的文件路径或模式，支持通配符*和?
        directory (str): 要在哪个目录下查找文件，可选值：html、code、data、all
        recursive (bool): 是否递归删除子目录中的文件

    Returns:
        dict: 包含删除结果的字典
    """
    try:
        # 确定要搜索的目录列表
        search_dirs = []
        if directory == "all":
            search_dirs = [HTML_DIR, CODE_DIR, DATA_DIR]
        elif directory == "html":
            search_dirs = [HTML_DIR]
        elif directory == "code":
            search_dirs = [CODE_DIR]
        elif directory == "data":
            search_dirs = [DATA_DIR]
        else:
            raise ValueError(f"无效的目录类型: {directory}")

        deleted_files = []
        deleted_dirs = []
        errors = []

        for base_dir in search_dirs:
            # 构建完整的搜索模式
            if os.path.isabs(path_pattern):
                search_pattern = path_pattern
            else:
                search_pattern = os.path.join(base_dir, "**", path_pattern) if recursive else os.path.join(base_dir,
                                                                                                           path_pattern)

            # 查找匹配的文件和目录
            matches = glob.glob(search_pattern, recursive=recursive)

            for match in matches:
                try:
                    if os.path.isfile(match):
                        os.remove(match)
                        deleted_files.append(match)
                    elif os.path.isdir(match):
                        shutil.rmtree(match)
                        deleted_dirs.append(match)
                except Exception as e:
                    errors.append(f"删除 {match} 时出错: {str(e)}")

        # 构建结果消息
        result_message = []
        if deleted_files:
            result_message.append(f"已删除文件:\n" + "\n".join([f"- {f}" for f in deleted_files]))
        if deleted_dirs:
            result_message.append(f"已删除目录:\n" + "\n".join([f"- {d}" for d in deleted_dirs]))
        if errors:
            result_message.append(f"错误信息:\n" + "\n".join([f"- {e}" for e in errors]))
        if not deleted_files and not deleted_dirs:
            result_message.append(f"未找到匹配的文件或目录: {path_pattern}")

        return {
            "message": "\n\n".join(result_message),
            "deleted_files": deleted_files,
            "deleted_dirs": deleted_dirs,
            "errors": errors
        }

    except Exception as e:
        raise Exception(f"删除文件时出错: {str(e)}")


def extract_file_path_by_ai(result_message, model="859-gpt-4_1__2025-04-14"):
    r"""
    让大模型提取文本中的本地文件路径（如 D:\\xxx\\yyy.csv），只返回路径字符串。
    """
    try:
        client = OpenAI(api_key=trip_apikey, base_url=trip_baseUrl)
        logger.info(f"OpenAI客户端初始化成功，使用API基础URL: {trip_baseUrl}")
    except Exception as e:
        logger.error(f"OpenAI客户端初始化失败: {str(e)}")
        client = None

    prompt = (
        r"请从下面这段文本中提取本地文件路径（如 D:\\xxx\\yyy.csv），"
        r"只返回路径字符串，不要返回其他内容。如果没有路径，返回空字符串。\n"
        f"文本：{result_message}"
    )
    messages = [{"role": "user", "content": prompt}]
    completion = client.chat.completions.create(
        model=model,
        messages=messages
    )
    # 清洗返回内容
    path = completion.choices[0].message.content.strip()
    # 去除引号和多余空白
    path = path.strip().strip('"').strip("'").strip()
    # 只取第一行
    path = path.splitlines()[0]
    return path


def clean_html(html_path, save_name=None):
    """
    智能HTML清洗函数 - 保留有价值内容，移除无用元素

    采用分层清洗策略：
    1. 移除绝对无用的标签（script、style等）
    2. 保护重要结构化数据（表格、列表、导航等）
    3. 基于内容价值评估移除低价值元素
    4. 智能处理可能有用的结构元素

    Args:
        html_path: HTML文件路径
        save_name: 保存文件名（可选）

    Returns:
        dict: 包含清洗结果和统计信息
    """
    from lxml import html, etree
    import re

    # sid = getattr(request, 'sid', None)
    # if emit and sid:
    #     emit('progress_update', {'step': 'clean_html', 'message': '正在智能清洗HTML内容...'}, to=sid)

    try:
        # 文件路径处理
        if not os.path.isabs(html_path):
            html_path = os.path.join(HTML_DIR, html_path)
        if not os.path.exists(html_path):
            raise FileNotFoundError(f"找不到HTML文件: {html_path}")

        # 读取HTML内容
        with open(html_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析HTML
        tree = html.fromstring(content)
        original_elements = len(tree.xpath('//*'))

        # 第一步：移除绝对无用的标签
        tree = _remove_useless_tags(tree)

        # 第二步：清理追踪代码和无用属性
        tree = _cleanup_tracking_and_attributes(tree)

        # 第三步：识别和保护重要结构
        protected_elements = _identify_important_structures(tree)

        # 第四步：移除全站导航和重复结构
        tree = _remove_global_navigation(tree, protected_elements)

        # 第五步：智能清洗可疑元素
        tree = _smart_cleanup_suspicious_elements(tree, protected_elements)

        # 第六步：内容价值评估和清理
        tree = _cleanup_low_value_content(tree, protected_elements)

        # 第七步：移除冗余的轮播和复杂结构
        tree = _remove_redundant_carousels(tree, protected_elements)

        # 第八步：结构扁平化和最终优化
        tree = _final_optimization(tree)

        # 生成清洗报告
        cleaned_elements = len(tree.xpath('//*'))
        cleaning_report = {
            "original_elements": original_elements,
            "cleaned_elements": cleaned_elements,
            "removed_elements": original_elements - cleaned_elements,
            "removal_rate": f"{((original_elements - cleaned_elements) / original_elements * 100):.1f}%",
            "preserved_tables": len(tree.xpath('//table')),
            "preserved_lists": len(tree.xpath('//ul | //ol')),
            "preserved_forms": len(tree.xpath('//form')),
        }

        # 序列化为HTML
        cleaned_html = etree.tostring(tree, encoding='unicode', method='html')

        # 保存文件
        if save_name:
            save_path = os.path.join(HTML_DIR, save_name)
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_html)

            return {
                "message": f"HTML已智能清洗并保存至 {save_path}",
                "save_path": save_path,
                "cleaning_report": cleaning_report
            }

        return {
            "message": "HTML已智能清洗完成",
            "save_path": None,
            "html": cleaned_html,
            "cleaning_report": cleaning_report
        }

    except Exception as e:
        logger.error(f"智能清洗HTML时出错: {str(e)}")
        raise


def _remove_useless_tags(tree):
    """移除绝对无用的标签"""
    from lxml import etree

    # 绝对无用的标签
    USELESS_TAGS = [
        'script', 'style', 'meta', 'link', 'noscript',
        'iframe', 'embed', 'object', 'applet'
    ]

    # 移除这些标签及其内容
    etree.strip_elements(tree, *USELESS_TAGS, with_tail=False)

    return tree


def _cleanup_tracking_and_attributes(tree):
    """清理追踪代码和无用属性"""
    import re

    # 需要完全移除的属性模式（更激进的清理）
    REMOVE_ATTR_PATTERNS = [
        re.compile(r'^data-astro', re.I),
        re.compile(r'^data-tagular', re.I),
        re.compile(r'^data-gtm', re.I),
        re.compile(r'^data-analytics', re.I),
        re.compile(r'^data-track', re.I),
        re.compile(r'^data-testid', re.I),  # 测试ID
        re.compile(r'^data-component-name', re.I),  # 组件名称
        re.compile(r'^data-next-head', re.I),  # Next.js头部
        re.compile(r'^data-styled', re.I),  # styled-components
        re.compile(r'^data-click-track', re.I),  # 点击追踪
        re.compile(r'^custom_click_track', re.I),  # 自定义追踪
        re.compile(r'^aria-label$', re.I),  # 大部分aria-label对爬虫无用
        re.compile(r'^aria-controls$', re.I),
        re.compile(r'^aria-expanded$', re.I),
        re.compile(r'^aria-live$', re.I),
        re.compile(r'^aria-modal$', re.I),
        re.compile(r'^aria-selected$', re.I),
        re.compile(r'^role$', re.I),  # 角色属性对爬虫无用
        re.compile(r'^crossorigin$', re.I),
        re.compile(r'^async$', re.I),
        re.compile(r'^loading$', re.I),  # 图片懒加载
        re.compile(r'^tabindex$', re.I),  # 焦点顺序
        re.compile(r'^novalidate$', re.I),  # 表单验证
        re.compile(r'^autocomplete$', re.I),  # 自动完成
        re.compile(r'^target$', re.I),  # 链接目标
        re.compile(r'^rel$', re.I),  # 链接关系
    ]

    # 需要简化的CSS类名模式（保留有意义的部分）
    SIMPLIFY_CLASS_PATTERNS = [
        # Tailwind CSS类名简化
        re.compile(r'\b(grid-cols-\d+|gap-\d+|p-\d+|m-\d+|text-\w+|bg-\w+)\b'),
        re.compile(r'\b(flex|block|hidden|relative|absolute|fixed)\b'),
        re.compile(r'\b(w-\w+|h-\w+|min-h-\w+|max-w-\w+)\b'),
    ]

    for elem in tree.xpath('//*'):
        # 移除追踪相关属性
        attrs_to_remove = []
        for attr_name in elem.attrib:
            if any(pattern.match(attr_name) for pattern in REMOVE_ATTR_PATTERNS):
                attrs_to_remove.append(attr_name)

        for attr in attrs_to_remove:
            del elem.attrib[attr]

        # 移除内联样式（对爬虫无用且占用大量空间）
        if 'style' in elem.attrib:
            del elem.attrib['style']

        # 简化图片属性，只保留src和alt
        if elem.tag == 'img':
            attrs_to_keep = ['src', 'alt']
            attrs_to_remove_img = [attr for attr in elem.attrib if attr not in attrs_to_keep]
            for attr in attrs_to_remove_img:
                del elem.attrib[attr]

        # 更激进的class属性清理
        if 'class' in elem.attrib:
            class_value = elem.attrib['class']
            # 只保留有明确语义价值的类名
            simplified_classes = []
            for cls in class_value.split():
                # 保留有语义的类名，移除所有技术性类名
                if (len(cls) <= 15 and  # 更短的长度限制
                    not re.match(r'^sc-[a-f0-9]', cls) and  # styled-components类名
                    not re.match(r'^[a-zA-Z]{2,4}-[a-f0-9]', cls) and  # 哈希类名
                    not re.match(r'^(grid-|gap-|p-|m-|text-|bg-|border-)', cls) and  # Tailwind
                    not re.match(r'^(d-|col-|row-|flex-|align-|justify-)', cls) and  # Bootstrap
                    not re.match(r'^(MuiTouchRipple|mui-|Mui)', cls) and  # Material-UI
                    cls not in ['track-completed', 'custom_click_track', 'analytics-click'] and  # 追踪类名
                    # 只保留真正有语义的类名
                    any(keyword in cls.lower() for keyword in [
                        'hotel', 'property', 'room', 'booking', 'search', 'filter', 'sort',
                        'card', 'list', 'item', 'title', 'price', 'rating', 'distance',
                        'container', 'wrapper', 'section', 'header', 'footer', 'nav',
                        'button', 'link', 'form', 'input', 'select'
                    ])):
                    simplified_classes.append(cls)

            if simplified_classes:
                elem.attrib['class'] = ' '.join(simplified_classes)
            else:
                # 如果没有有意义的类名，删除class属性
                if 'class' in elem.attrib:
                    del elem.attrib['class']

    return tree


def _remove_global_navigation(tree, protected_elements):
    """移除全站导航和重复结构（但保护内容相关的导航）"""
    import re

    elements_to_remove = []

    # 识别全站导航的特征（更精确）
    GLOBAL_NAV_PATTERNS = [
        re.compile(r'(global-nav|main-nav|primary-nav|site-nav)', re.I),
        re.compile(r'(header-nav|top-nav)', re.I),
        re.compile(r'(mega-menu|dropdown-menu)', re.I),
    ]

    # 绝对不能删除的内容相关模式
    CONTENT_PATTERNS = [
        # 酒店和旅游相关
        re.compile(r'(hotel|catalog|property|resort|accommodation|booking|room)', re.I),
        # 票务相关
        re.compile(r'(ticket|item|product|event|show|performance)', re.I),
        # 分页和搜索
        re.compile(r'(paginator|pagination|pager|search|filter|facet)', re.I),
        # 内容区域
        re.compile(r'(list|content|section|results|details)', re.I),
        # Angular组件
        re.compile(r'(component|ng-|data-)', re.I),
    ]

    # 识别复杂导航结构
    for elem in tree.xpath('//*'):
        if elem in protected_elements:
            continue

        attrs = ' '.join([
            elem.get('class', ''),
            elem.get('id', ''),
            elem.get('role', ''),
            elem.tag
        ])

        # 首先检查是否包含内容相关的关键词，如果是则跳过
        is_content_related = any(pattern.search(attrs) for pattern in CONTENT_PATTERNS)
        if is_content_related:
            continue

        # 检查是否为Angular/Vue/React组件，如果是则跳过
        if '-' in elem.tag or elem.get('ng-version') or elem.get('data-component-type'):
            continue

        # 检查是否为全站导航
        is_global_nav = any(pattern.search(attrs) for pattern in GLOBAL_NAV_PATTERNS)

        if is_global_nav:
            elements_to_remove.append(elem)
            continue

        # 检查是否为复杂的导航结构（但要更谨慎）
        if elem.tag in ['nav'] or ('nav' in attrs.lower() and 'header' in attrs.lower()):
            # 计算导航复杂度
            links_count = len(elem.xpath('.//a'))
            nested_depth = len(elem.xpath('.//div//div//div'))
            submenu_count = len(elem.xpath('.//*[contains(@class, "submenu") or contains(@class, "dropdown")]'))

            # 检查是否包含酒店/票务相关内容
            text_content = elem.text_content().lower()
            has_valuable_content = any(keyword in text_content for keyword in
                                       ['開演', '受付', '公演', '劇場', '会場', 'チケット', '先着', '抽選',
                                        'hotel', 'resort', 'room', 'booking', 'property', 'catalog',
                                        'results found', 'select dates', 'pricing'])

            # 只有在确实是复杂导航且不包含有价值内容时才移除
            if (links_count > 25 and nested_depth > 6 and submenu_count > 4 and not has_valuable_content):
                elements_to_remove.append(elem)

    # 移除识别出的全站导航
    for elem in elements_to_remove:
        parent = elem.getparent()
        if parent is not None:
            parent.remove(elem)

    return tree


def _identify_important_structures(tree):
    """识别和标记重要结构元素"""
    important_elements = set()

    # 1. 结构化数据表格（有实际数据的表格）
    tables = tree.xpath('//table[.//td[normalize-space(text())]]')
    for table in tables:
        rows = table.xpath('.//tr')
        if len(rows) >= 2:  # 至少有2行数据
            important_elements.add(table)

    # 2. 酒店和旅游相关的重要结构（优先保护）
    hotel_selectors = [
        # 酒店相关
        '//*[contains(@class, "hotel")]',
        '//*[contains(@class, "catalog")]',
        '//*[contains(@class, "property")]',
        '//*[contains(@class, "resort")]',
        '//*[contains(@class, "accommodation")]',
        '//*[contains(@class, "booking")]',
        '//*[contains(@class, "room")]',
        # 票务相关
        '//*[contains(@class, "ticket")]',
        '//*[contains(@class, "item") and (contains(@class, "list") or contains(@class, "card"))]',
        '//*[contains(@class, "product")]',
        '//*[contains(@class, "event")]',
        '//*[contains(@class, "show")]',
        '//*[contains(@class, "performance")]',
        '//*[contains(@class, "concert")]',
        '//*[contains(@class, "block-ticket")]',
        '//*[contains(@class, "block-paginator")]',  # 分页器
    ]

    for selector in hotel_selectors:
        elements = tree.xpath(selector)
        important_elements.update(elements)
        # 同时保护这些元素的所有祖先元素
        for elem in elements:
            parent = elem.getparent()
            while parent is not None:
                important_elements.add(parent)
                parent = parent.getparent()

    # 3. Angular/Vue/React组件（现代Web应用的核心内容）
    component_selectors = [
        # Angular组件
        '//hotel-catalog',
        '//hotel-search-result',
        '//search-form',
        '//catalog-booking',
        '//catalog-booking-security',
        '//compare-button',
        '//amenities',
        '//room-list',
        '//facets',
        '//pagination',
        '//results-count',
        '//sort',
        # 通用组件模式
        '//*[contains(local-name(), "-") and string-length(local-name()) > 3]',  # 自定义元素
        '//*[@ng-version]',  # Angular应用
        '//*[@data-component-type]',  # 组件标识
    ]

    for selector in component_selectors:
        elements = tree.xpath(selector)
        important_elements.update(elements)
        # 保护组件的所有子元素和祖先元素
        for elem in elements:
            # 保护所有子元素
            descendants = elem.xpath('.//*')
            important_elements.update(descendants)
            # 保护所有祖先元素
            parent = elem.getparent()
            while parent is not None:
                important_elements.add(parent)
                parent = parent.getparent()

    # 4. 有意义的列表（超过2个项目且不是纯导航列表）
    lists = tree.xpath('//ul[count(li) > 2] | //ol[count(li) > 2]')
    for lst in lists:
        attrs = ' '.join([lst.get('class', ''), lst.get('id', '')])
        # 如果包含酒店/票务相关关键词，一定保护
        if any(keyword in attrs.lower() for keyword in
               ['hotel', 'catalog', 'ticket', 'item', 'product', 'event', 'facet']):
            important_elements.add(lst)
        # 排除纯导航列表，但保留可能包含内容的列表
        elif not any(nav_word in attrs.lower() for nav_word in ['global-nav', 'main-nav', 'header-nav']):
            # 检查列表项内容，如果包含有价值信息则保护
            list_text = lst.text_content().strip()
            if any(keyword in list_text for keyword in
                   ['開演', '受付', '公演', '劇場', '会場', '日程', 'Hotel', 'Resort', 'Room']):
                important_elements.add(lst)

    # 5. 表单元素（特别保护搜索表单）
    forms = tree.xpath('//form')
    important_elements.update(forms)

    # 特别保护搜索相关的元素
    search_selectors = [
        '//form[contains(@action, "/s") or contains(@action, "search")]',  # 搜索表单
        '//input[@type="text" and (@name="wd" or @name="q" or @name="query" or @name="search")]',  # 搜索输入框
        '//input[@type="submit" and (contains(@value, "搜索") or contains(@value, "百度一下") or contains(@value, "Search"))]',  # 搜索按钮
        '//*[contains(@class, "search") or contains(@id, "search")]',  # 搜索相关容器
        '//*[contains(@class, "s_ipt") or contains(@class, "s_btn")]',  # 百度特有的搜索样式
        '//*[@id="kw" or @id="su" or @id="form"]',  # 百度搜索的特定ID
    ]

    for selector in search_selectors:
        elements = tree.xpath(selector)
        important_elements.update(elements)
        # 保护搜索元素的所有祖先和子元素
        for elem in elements:
            # 保护所有子元素
            descendants = elem.xpath('.//*')
            important_elements.update(descendants)
            # 保护所有祖先元素
            parent = elem.getparent()
            while parent is not None:
                important_elements.add(parent)
                parent = parent.getparent()

    # 6. 分页导航（内容相关的分页）
    pagination_selectors = [
        '//*[contains(@class, "pagination")]',
        '//*[contains(@class, "paginator")]',
        '//*[contains(@class, "pager")]',
        '//*[contains(text(), "前へ") or contains(text(), "次へ")]',
        '//*[contains(text(), "前のページ") or contains(text(), "次のページ")]',
        '//*[contains(text(), "←") or contains(text(), "→")]',
        '//*[contains(text(), "Results Found")]',
        '//*[@data-max-page]'  # 分页输入框
    ]

    for selector in pagination_selectors:
        elements = tree.xpath(selector)
        important_elements.update(elements)

    # 7. 主要内容区域
    main_content = tree.xpath(
        '//main | //article | //*[@role="main"] | //section[contains(@class, "section")] | //*[@id="catalog-container"]')
    important_elements.update(main_content)

    # 8. 包含关键信息的元素
    valuable_selectors = [
        '//*[contains(text(), "￥") or contains(text(), "¥") or contains(text(), "$")]',
        '//*[contains(@class, "price") or contains(@class, "cost")]',
        '//*[contains(@class, "date") or contains(@class, "time")]',
        '//*[contains(@class, "venue") or contains(@class, "location")]',
        '//*[contains(@class, "status")]',
        '//*[contains(@class, "title") and not(ancestor::*[contains(@class, "header")])]',
        '//*[contains(@class, "description") or contains(@class, "summary")]',
        '//*[contains(@class, "details")]',
        '//*[contains(@class, "attributes")]'
    ]

    for selector in valuable_selectors:
        elements = tree.xpath(selector)
        important_elements.update(elements)

    return important_elements


def _smart_cleanup_suspicious_elements(tree, protected_elements):
    """智能清洗可疑元素"""
    import re

    # 可疑但不绝对删除的模式
    SUSPICIOUS_PATTERNS = [
        re.compile(r'(ad-|ads-|banner|popup|modal)', re.I),
        re.compile(r'(copyright|privacy|cookie|terms)', re.I),
        re.compile(r'(share|social|follow)', re.I),
        re.compile(r'(invisible|hidden|collapse|none)', re.I)
    ]

    elements_to_remove = []

    for elem in tree.xpath('//*'):
        # 跳过受保护的元素
        if elem in protected_elements:
            continue

        # 检查元素属性
        attrs = ' '.join([
            elem.get('class', ''),
            elem.get('id', ''),
            elem.get('role', ''),
            elem.get('data-role', '')
        ])

        # 检查是否匹配可疑模式
        is_suspicious = any(pattern.search(attrs) for pattern in SUSPICIOUS_PATTERNS)

        if is_suspicious:
            # 进一步检查元素价值
            if _evaluate_element_value(elem) < 0.3:
                elements_to_remove.append(elem)

    # 移除低价值的可疑元素
    for elem in elements_to_remove:
        parent = elem.getparent()
        if parent is not None:
            parent.remove(elem)

    return tree


def _evaluate_element_value(elem):
    """评估元素的内容价值（0-1之间，1为最高价值）- 保护酒店和票务内容"""
    try:
        text_content = elem.text_content().strip()
        attrs = ' '.join([elem.get('class', ''), elem.get('id', ''), elem.tag])

        # 空内容或极短内容
        if not text_content or len(text_content) < 3:
            # 但是对于Angular组件等，即使没有文本也可能很重要
            if '-' in elem.tag or elem.get('ng-version') or elem.get('data-component-type'):
                return 0.7  # Angular组件给中等分数
            return 0.05

        # 酒店和旅游相关元素直接给高分（优先保护）
        hotel_keywords_in_attrs = [
            'hotel', 'catalog', 'property', 'resort', 'accommodation', 'booking', 'room',
            'ticket', 'item', 'product', 'event', 'show', 'performance', 'paginator',
            'search-result', 'facets', 'amenities', 'details', 'attributes'
        ]
        if any(keyword in attrs.lower() for keyword in hotel_keywords_in_attrs):
            return 0.95  # 酒店/票务相关元素给最高分

        # Angular/Vue/React组件给高分
        if '-' in elem.tag or elem.get('ng-version') or elem.get('data-component-type'):
            return 0.9  # 现代Web组件给高分

        # 酒店和票务相关内容关键词
        valuable_content_keywords = [
            # 日文票务关键词
            '開演', '受付', '公演', '劇場', '会場', 'チケット', '先着', '抽選',
            'ミュージカル', '舞台', '演劇', '公演日', '開場', '終了',
            '前へ', '次へ', 'ページ', '件中', '件表示',
            # 英文酒店关键词
            'Hotel', 'Resort', 'Room', 'Booking', 'Property', 'Accommodation',
            'Select Dates', 'Pricing', 'Results Found', 'Number of Rooms',
            'Room Style', 'Vibe', 'Nearest Airport', 'Compare', 'Amenities',
            'Contemporary', 'Classic', 'Sophisticated', 'Zen', 'Hip', 'Casual'
        ]

        # 检查是否包含有价值内容关键词
        valuable_content_score = 0
        for keyword in valuable_content_keywords:
            if keyword in text_content:
                valuable_content_score += 0.15

        if valuable_content_score > 0.3:  # 包含多个有价值关键词
            return 0.9  # 给高分保护

        # 检查是否只包含链接文本（导航元素特征）
        links = elem.xpath('.//a')
        if links:
            link_text_length = sum(len(link.text_content().strip()) for link in links)
            # 对于酒店/票务列表，链接多是正常的，不应该惩罚
            if valuable_content_score > 0:
                link_penalty = 0.95  # 有价值内容的链接不惩罚
            elif link_text_length > len(text_content) * 0.8:  # 80%以上是链接文本
                link_penalty = 0.3  # 很可能是纯导航元素
            else:
                link_penalty = 0.7
        else:
            link_penalty = 1.0

        # 文本长度评分
        if len(text_content) < 10:
            length_score = 0.3
        elif len(text_content) < 30:
            length_score = 0.5
        else:
            length_score = min(len(text_content) / 80, 1.0)  # 80字符为满分

        # 关键词价值评分
        high_value_keywords = ['价格', '￥', '¥', '$', '元', '费用', '成本', 'USD', 'EUR']
        medium_value_keywords = ['标题', '名称', '描述', '详情', '内容', 'Title', 'Name', 'Description']
        time_keywords = ['时间', '日期', '年', '月', '日', '2025', '2024', 'Check In', 'Check Out']

        keyword_score = 0
        for keyword in high_value_keywords:
            if keyword in text_content:
                keyword_score += 0.25
        for keyword in medium_value_keywords:
            if keyword in text_content:
                keyword_score += 0.15
        for keyword in time_keywords:
            if keyword in text_content:
                keyword_score += 0.1
        keyword_score = min(keyword_score, 1.0)

        # 结构价值评分
        structure_score = 0
        if elem.tag in ['table', 'ul', 'ol', 'dl']:
            structure_score = 0.8
        elif elem.tag in ['article', 'section', 'aside']:
            structure_score = 0.9
        elif elem.tag in ['div']:
            # 检查div是否为内容容器
            if any(keyword in attrs.lower() for keyword in ['content', 'list', 'item', 'block', 'card', 'container']):
                structure_score = 0.7
            else:
                structure_score = 0.4
        elif elem.tag in ['p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            structure_score = 0.6
        elif '-' in elem.tag:  # 自定义元素/组件
            structure_score = 0.9

        # 综合评分（给有价值内容更多权重）
        final_score = (length_score * 0.15 +
                       keyword_score * 0.25 +
                       structure_score * 0.25 +
                       valuable_content_score * 0.35) * link_penalty

        return min(final_score, 1.0)

    except Exception:
        return 0.2  # 出错时返回低价值


def _cleanup_low_value_content(tree, protected_elements):
    """清理低价值内容（保护酒店和票务内容的宽松标准）"""
    elements_to_remove = []

    for elem in tree.xpath('//*'):
        # 跳过受保护的元素
        if elem in protected_elements:
            continue

        # 跳过重要的结构标签
        if elem.tag in ['html', 'head', 'body', 'main', 'article', 'section', 'aside']:
            continue

        # 跳过Angular/Vue/React组件
        if '-' in elem.tag or elem.get('ng-version') or elem.get('data-component-type'):
            continue

        # 评估元素价值
        value_score = _evaluate_element_value(elem)
        attrs = ' '.join([elem.get('class', ''), elem.get('id', ''), elem.tag])
        text_content = elem.text_content().strip()

        # 酒店和票务相关元素绝对不删除
        valuable_protection_keywords = [
            # 酒店相关
            'hotel', 'catalog', 'property', 'resort', 'accommodation', 'booking', 'room',
            'search-result', 'facets', 'amenities', 'details', 'attributes', 'pagination',
            # 票务相关
            'ticket', 'item', 'product', 'event', 'paginator', 'list',
            # 数据相关
            'data-ecommerce', 'data-virtuoso', 'scroll-beacon'
        ]
        if any(keyword in attrs.lower() for keyword in valuable_protection_keywords):
            continue

        # 包含酒店/票务内容的元素不删除
        valuable_content_keywords = [
            # 日文票务关键词
            '開演', '受付', '公演', '劇場', '会場', 'チケット', '先着', '抽選', 'ミュージカル',
            # 英文酒店关键词
            'Hotel', 'Resort', 'Room', 'Booking', 'Property', 'Select Dates', 'Pricing',
            'Results Found', 'Number of Rooms', 'Room Style', 'Vibe', 'Nearest Airport',
            'Compare', 'Amenities', 'Contemporary', 'Classic', 'Sophisticated'
        ]
        if any(keyword in text_content for keyword in valuable_content_keywords):
            continue

        # 非常宽松的清理标准
        should_remove = False

        # 只有价值极低的才删除
        if value_score < 0.05:
            should_remove = True

        # 特殊情况：明显的无用元素
        elif value_score < 0.15:
            # 移除空容器
            if len(text_content) < 2:
                should_remove = True

            # 移除明显的追踪和广告元素
            elif any(ad_word in attrs.lower() for ad_word in ['tracking', 'analytics', 'gtm', 'ads', 'pixel']):
                should_remove = True

            # 移除只有很少文本且明显是装饰性的元素
            elif len(text_content) < 5 and any(
                    deco_word in attrs.lower() for deco_word in ['decoration', 'spacer', 'divider', 'separator']):
                should_remove = True

            # 移除明显的脚本和样式相关元素
            elif elem.tag in ['script', 'style', 'noscript'] and 'essential' not in attrs.lower():
                should_remove = True

        if should_remove:
            elements_to_remove.append(elem)

    # 移除低价值元素
    for elem in elements_to_remove:
        parent = elem.getparent()
        if parent is not None:
            parent.remove(elem)

    return tree


def _remove_redundant_carousels(tree, protected_elements):
    """移除冗余的轮播和复杂展示结构，只保留第一张图片"""
    elements_to_remove = []

    # 查找轮播容器
    carousel_containers = tree.xpath('//*[contains(@class, "carousel") or contains(@class, "slider") or contains(@class, "glide")]')

    for container in carousel_containers:
        if container in protected_elements:
            continue

        # 查找轮播中的图片
        images = container.xpath('.//img')
        if len(images) > 1:
            # 只保留第一张图片，移除其他图片的容器
            first_img = images[0]

            # 找到第一张图片的最近容器
            first_img_container = first_img.getparent()
            while first_img_container and first_img_container != container:
                if 'slide' in first_img_container.get('class', ''):
                    break
                first_img_container = first_img_container.getparent()

            # 移除其他图片容器
            for img in images[1:]:
                img_container = img.getparent()
                while img_container and img_container != container:
                    if 'slide' in img_container.get('class', ''):
                        elements_to_remove.append(img_container)
                        break
                    img_container = img_container.getparent()

    # 移除冗余的轮播控制元素
    carousel_controls = tree.xpath('//*[contains(@class, "carousel__") or contains(@class, "slider") or contains(@class, "sliderTray")]')
    for control in carousel_controls:
        if control in protected_elements:
            continue
        # 如果控制元素没有有价值的文本内容，移除它
        if len(control.text_content().strip()) < 10:
            elements_to_remove.append(control)

    # 执行移除
    for elem in elements_to_remove:
        parent = elem.getparent()
        if parent is not None:
            parent.remove(elem)

    return tree


def _final_optimization(tree):
    """最终优化处理 - 结构扁平化和深度清理"""
    # 1. 移除空元素（但保留某些可能有用的空元素）
    PRESERVE_EMPTY = ['br', 'hr', 'img', 'input', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr']

    # 多轮清理空元素，因为移除后可能产生新的空元素
    for _ in range(3):
        empty_elements = []
        for elem in tree.xpath('//*'):
            if (elem.tag not in PRESERVE_EMPTY and
                    not elem.text_content().strip() and
                    len(elem) == 0):
                empty_elements.append(elem)

        for elem in empty_elements:
            parent = elem.getparent()
            if parent is not None:
                parent.remove(elem)

        if not empty_elements:  # 如果没有找到空元素，停止循环
            break

    # 2. 清理多余的空白
    for elem in tree.xpath('//*'):
        if elem.text:
            elem.text = ' '.join(elem.text.split())
        if elem.tail:
            elem.tail = ' '.join(elem.tail.split())

    # 3. 结构扁平化 - 移除无意义的嵌套容器
    for _ in range(3):  # 多轮处理
        redundant_containers = []

        # 移除只有一个子元素的无意义容器
        for elem in tree.xpath('//div | //span'):
            if (len(elem) == 1 and
                    elem[0].tag in ['div', 'span', 'p'] and
                    not elem.get('class') and
                    not elem.get('id') and
                    not elem.text_content().strip()):
                redundant_containers.append(elem)

        # 移除只包含文本且无属性的多余span
        for elem in tree.xpath('//span'):
            if (len(elem) == 0 and
                    not elem.get('class') and
                    not elem.get('id') and
                    elem.text_content().strip()):
                # 将文本内容合并到父元素
                parent = elem.getparent()
                if parent is not None:
                    if parent.text:
                        parent.text += ' ' + elem.text_content()
                    else:
                        parent.text = elem.text_content()
                    redundant_containers.append(elem)

        for elem in redundant_containers:
            parent = elem.getparent()
            if parent is not None:
                if len(elem) == 1:
                    # 用子元素替换当前元素
                    child = elem[0]
                    parent.replace(elem, child)
                else:
                    # 直接移除
                    parent.remove(elem)

        if not redundant_containers:  # 如果没有找到冗余容器，停止循环
            break

    # 4. 移除过度嵌套的div结构
    deeply_nested = tree.xpath('//div//div//div//div//div')
    for elem in deeply_nested:
        # 如果深度嵌套的div没有有意义的属性和内容，考虑移除
        if (not elem.get('class') and
                not elem.get('id') and
                len(elem.text_content().strip()) < 10):
            parent = elem.getparent()
            if parent is not None:
                # 将子元素移动到父元素
                for child in elem:
                    parent.append(child)
                parent.remove(elem)

    return tree


if __name__ == '__main__':
    # open_and_save_page_with_remote_browser("https://www.xiaohongshu.com/explore ", "Xhs.html")
    # open_and_save_page("https://www.expedia.com/ ", "expedia.html", False)
    # clean_html("marriott_shanghai_list.html","marriott_shanghai_list_clean.html")
    # virtuoso_url = "https://www.virtuoso.com/hotels#CurrentPage=1&FacetCategoryIndex=0&FacetLimit=6&LeftToShow=0&RowsPerPage=25&SearchView=1col&StartRow=0&HotelBookingNumberChildren=0&HotelBookingNumberAdults=2&SearchType=Property&SortType=HotelNameAsc"
    # jam_base = "https://www.jambase.com/festivals/all/page/2"
    # open_and_save_page(virtuoso_url, "jame_base_4.html", False)

    # url = "https://www.marriott.com.cn/search/findHotels.mi?fromToDate_submit=07/26/2025&fromDate=2025-07-24&toDate=2025-07-26&toDateDefaultFormat=07/26/2025&fromDateDefaultFormat=07/24/2025&flexibleDateSearch=false&t-start=2025-07-24&t-end=2025-07-26&lengthOfStay=3&childrenCountBox=0+Children+Per+Room&childrenCount=0&clusterCode=none&isAdvanceSearch=true&recordsPerPage=40&isInternalSearch=true&vsInitialRequest=false&searchType=InCity&singleSearchAutoSuggest=Unmatched&for-hotels-nearme=Near&collapseAccordian=is-hidden&singleSearch=true&isTransient=true&initialRequest=true&flexibleDateSearchRateDisplay=false&isSearch=true&isRateCalendar=true&destinationAddress.destination=%E4%B8%8A%E6%B5%B7&isHideFlexibleDateCalendar=false&roomCountBox=1+Room&roomCount=1&guestCountBox=1+Adult+Per+Room&numAdultsPerRoom=1&deviceType=desktop-web&view=list&destinationAddress.latitude=31.230416&destinationAddress.location=%E4%B8%8A%E6%B5%B7&destinationAddress.stateProvince=%E4%B8%8A%E6%B5%B7%E5%B8%82&searchRadius=50&destinationAddress.placeId=ChIJMzz1sUBwsjURoWTDI5QSlQI&destinationAddress.country=CN&destinationAddress.address=%E4%B8%AD%E5%9B%BD%E4%B8%8A%E6%B5%B7%E5%B8%82&destinationAddress.city=%E4%B8%8A%E6%B5%B7%E5%B8%82&destinationAddress.mainText=%E4%B8%8A%E6%B5%B7%E5%B8%82&destinationAddress.longitude=121.473701&destinationAddress.type=City&fromToDate=07/24/2025&isFlexibleDatesOptionSelected=false&numberOfRooms=1#/0/"
    # save_name = url.split('/')[-1] + ".html"
    # open_and_save_page(url, "marriott.html", headless=False)
    # clean_save_name = save_name.split('.')[0] + "_cleaned.html"
    # clean_html(save_name, clean_save_name)

    # clean_html("eplus_p3_raw.html", "eplus_p3_r33aw.html")
    # --- 使用示例 ---
    # HTML_DIR = "/path/to/your/html/files" # 请设置你的 HTML 目录
    # result = clean_html("virtuoso.html", "virtuoso_clean2.html")
    # result = clean_html("marriott.html", "marriott_clean.html")
    # result = clean_html("baidu_home_raw.html", "baidu_home_raw_clean3.html")
    result = clean_html("eplus_2_5musical_p3.html", "eplus_2_5musical_p3__.html")
    # result = clean_html("eplus_2_5musical_p3.html", "eplus_2_5musical_p3__.html")
    # print(result['cleaning_report'])
    # print(result['html']) # 或者从 result['save_path'] 读取
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iCrawler-您的智能数据抓取助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <div class="container">
        <h1 class="my-4 text-center">iCrawler-您的智能数据抓取助手</h1>
        <div class="d-flex justify-content-end mb-3">
            <button id="clearHistory" class="btn btn-outline-warning me-2" title="清除当前会话的上下文和历史记录">
                <i class="bi bi-trash"></i> 清除当前上下文
            </button>
            <a href="/history" class="btn btn-outline-secondary"><i class="bi bi-clock-history"></i> 历史任务/结果</a>
        </div>
        <div class="row">
            <div class="col-md-8">
                <!-- 输入卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-robot"></i> 输入需要执行的任务
                    </div>
                    <div class="card-body">
                        <form id="promptForm">
                            <!-- 基础信息：默认展开 -->
                            <div class="mb-3">
                                <label for="taskUrl" class="form-label required-field">目标网址 (URL)：</label>
                                <input type="text" class="form-control" id="taskUrl" placeholder="例如：https://www.taoart.com/information/list.htm">
                            </div>
                            <div class="mb-3">
                                <label for="taskType" class="form-label required-field">任务类型：</label>
                                <select class="form-select" id="taskType">
                                    <option value="list_detail">列表详情类型</option>
                                    <option value="search">搜索类型</option>
                                    <option value="single_page" selected>单页类型</option>
                                    <option value="custom">其他类型</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="dataRequirements" class="form-label required-field">数据要求内容：</label>
                                <textarea class="form-control" id="dataRequirements" rows="2" placeholder="需要采集哪些字段？"></textarea>
                            </div>

                            <!-- 折叠按钮 -->
                            <button class="btn btn-outline-info d-flex align-items-center mb-3" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#advancedOptions"
                                    aria-expanded="false" aria-controls="advancedOptions" id="toggleAdvancedBtn">
                                <span class="me-2"><i class="bi bi-sliders"></i></span>
                                <span>展开高级选项</span>
                                <span class="ms-2 transition" id="advancedArrow"><i class="bi bi-chevron-down"></i></span>
                            </button>
                            <div class="collapse" id="advancedOptions">
                                <div class="card card-body bg-light mb-3">
                                    <!-- 数据保存格式 -->
                                    <div class="mb-3 border-bottom pb-2 mb-2">
                                        <label for="dataFormat" class="form-label fw-bold">数据保存格式：</label>
                                        <select class="form-select" id="dataFormat">
                                            <option value="csv">CSV 文件</option>
                                            <option value="json">JSON 文件</option>
                                            <option value="excel">Excel 文件</option>
                                            <option value="txt">文本文件</option>
                                        </select>
                                    </div>
                                    <!-- 格式配置分组 -->
                                    <div id="formatOptions">
                                        <div id="csvOptions" class="format-options">
                                            <div class="fw-bold text-primary mb-1">CSV 配置</div>
                                            <div class="row g-2 mb-2">
                                                <div class="col-md-6">
                                                    <label for="csvColumns" class="form-label">列名（逗号分隔）：</label>
                                                    <input type="text" class="form-control" id="csvColumns" placeholder="标题,时间,地点,链接,简介">
                                                </div>
                                                <div class="col-md-3 d-flex align-items-end">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="csvIncludeHeader" checked>
                                                        <label class="form-check-label" for="csvIncludeHeader">包含表头</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="csvEncoding" class="form-label">编码：</label>
                                                    <select class="form-select" id="csvEncoding">
                                                        <option value="utf-8">UTF-8</option>
                                                        <option value="utf-8-sig">UTF-8 (带BOM)</option>
                                                        <option value="gbk">GBK</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="jsonOptions" class="format-options hidden">
                                            <div class="fw-bold text-primary mb-1">JSON 配置</div>
                                            <div class="row g-2 mb-2">
                                                <div class="col-md-6">
                                                    <label for="jsonStructure" class="form-label">结构：</label>
                                                    <select class="form-select" id="jsonStructure">
                                                        <option value="records">对象数组</option>
                                                        <option value="nested">嵌套对象</option>
                                                        <option value="lines">每行一个对象 (JSONL)</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="jsonFields" class="form-label">字段（逗号分隔）：</label>
                                                    <input type="text" class="form-control" id="jsonFields" placeholder="title,date,location,url,description">
                                                </div>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="jsonPretty" checked>
                                                <label class="form-check-label" for="jsonPretty">美化输出</label>
                                            </div>
                                        </div>
                                        <div id="excelOptions" class="format-options hidden">
                                            <div class="fw-bold text-primary mb-1">Excel 配置</div>
                                            <div class="row g-2 mb-2">
                                                <div class="col-md-6">
                                                    <label for="excelColumns" class="form-label">列名（逗号分隔）：</label>
                                                    <input type="text" class="form-control" id="excelColumns" placeholder="标题,时间,地点,链接,简介">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="excelSheetName" class="form-label">工作表名称：</label>
                                                    <input type="text" class="form-control" id="excelSheetName" placeholder="Sheet1" value="数据">
                                                </div>
                                            </div>
                                            <div class="row g-2 mb-2">
                                                <div class="col-md-6 d-flex align-items-end">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="excelAutoFilter" checked>
                                                        <label class="form-check-label" for="excelAutoFilter">添加筛选</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 d-flex align-items-end">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="excelFreeze" checked>
                                                        <label class="form-check-label" for="excelFreeze">冻结首行</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="txtOptions" class="format-options hidden">
                                            <div class="fw-bold text-primary mb-1">文本文件配置</div>
                                            <div class="row g-2 mb-2">
                                                <div class="col-md-6">
                                                    <label for="txtSeparator" class="form-label">分隔符：</label>
                                                    <select class="form-select" id="txtSeparator">
                                                        <option value="|">竖线 |</option>
                                                        <option value="\t">制表符 Tab</option>
                                                        <option value=" ">空格</option>
                                                        <option value="custom">自定义</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6" id="customSeparatorDiv" class="hidden">
                                                    <label for="customSeparator" class="form-label">自定义分隔符：</label>
                                                    <input type="text" class="form-control" id="customSeparator" maxlength="5">
                                                </div>
                                            </div>
                                            <div class="row g-2 mb-2">
                                                <div class="col-md-6">
                                                    <label for="txtEncoding" class="form-label">编码：</label>
                                                    <select class="form-select" id="txtEncoding">
                                                        <option value="utf-8">UTF-8</option>
                                                        <option value="utf-8-sig">UTF-8 (带BOM)</option>
                                                        <option value="gbk">GBK</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 翻页选项 -->
                                    <div class="mb-3 border-top pt-2 mt-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="needPagination">
                                            <label class="form-check-label" for="needPagination">需要翻页</label>
                                        </div>
                                        <div id="paginationOptions" class="mt-2 ps-4 hidden">
                                            <label for="pageCount" class="form-label">翻页数量：</label>
                                            <select class="form-select" id="pageCount">
                                                <option value="1">1页</option>
                                                <option value="2">2页</option>
                                                <option value="3" selected>3页</option>
                                                <option value="5">5页</option>
                                                <option value="10">10页</option>
                                                <option value="all">所有页</option>
                                            </select>
                                        </div>
                                    </div>
                                    <!-- 其他特殊要求 -->
                                    <div class="mb-3">
                                        <label for="additionalRequirements" class="form-label">其他特殊要求（可选）：</label>
                                        <textarea class="form-control" id="additionalRequirements" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- 自动确认函数执行 -->
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="autoConfirmFunction" checked>
                                <label class="form-check-label" for="autoConfirmFunction">自动确认函数执行</label>
                            </div>
                            <!-- 提交按钮 -->
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-play-fill"></i> 开始生成并执行代码
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 生成代码展示区 -->
                <div class="card my-3" id="codeCard" style="display:none;">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <span>
                            <i class="bi bi-file-earmark-code"></i> 生成的代码
                        </span>
                        <div>
                            <button class="btn btn-sm btn-outline-light me-2" id="downloadCodeBtn">
                                <i class="bi bi-download"></i> 下载
                            </button>
                            <button class="btn btn-sm btn-outline-light me-2" id="copyCodeBtn">
                                <i class="bi bi-clipboard"></i> 复制
                            </button>
                            <button class="btn btn-sm btn-outline-light toggle-btn" id="toggleCodeCardBtn" type="button">
                                <i class="bi bi-chevron-up" id="codeCardArrow"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body" id="codeCardBody">
                        <pre id="generatedCode" class="code-block"></pre>
                    </div>
                </div>
                <!-- 生成结果预览展示区 -->
                <div class="card mb-3" id="resultCard" style="display:none;">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <span>
                            <i class="bi bi-flag"></i> 生成结果预览
                        </span>
                        <div>
                            <button class="btn btn-sm btn-outline-light me-2" id="downloadResultBtn">
                                <i class="bi bi-download"></i> 下载
                            </button>
                            <button class="btn btn-sm btn-outline-light me-2" id="copyResultBtn">
                                <i class="bi bi-clipboard"></i> 复制
                            </button>
                            <button class="btn btn-sm btn-outline-light toggle-btn" id="toggleResultCardBtn" type="button">
                                <i class="bi bi-chevron-up" id="resultCardArrow"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body" id="resultCardBody">
                        <pre id="generatedResult" class="bg-light rounded p-2" style="max-height:300px;overflow:auto;"></pre>
                    </div>
                </div>

                <!-- 进度卡片 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>
                            <i class="bi bi-list-check"></i> 执行进度
                        </span>
                        <button id="stopTaskBtn" class="btn btn-danger btn-sm fw-bold shadow-sm" style="display: none;" title="立即停止当前正在执行的任务">
                            <i class="bi bi-stop-circle-fill me-1"></i>
                            <span class="d-none d-md-inline">停止任务</span>
                            <span class="d-md-none">停止</span>
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="progressLog" class="progress-log">
                            <!-- <div id="progressSpinner"></div> -->
                            <ul id="progressSteps" class="list-unstyled mb-0"></ul>
                        </div>
                    </div>
                </div>

                <!-- 任务完成后的继续交互卡片 -->
                <div class="card" id="continueInteractionCard" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <i class="bi bi-chat-dots"></i> 继续与AI交互
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-3">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>任务已完成！</strong> 您可以继续与AI进行交互。

                            <div class="mt-3">
                                <div class="alert alert-info mb-2 py-2">
                                    <i class="bi bi-lightning-fill me-1"></i>
                                    <small><strong>智能执行模式：</strong>AI会根据您的需求主动执行相关操作，无需额外确认</small>
                                </div>

                                <strong>您可以尝试：</strong>
                                <ul class="mt-2 mb-0">
                                    <li>分析这些数据的价值和意义</li>
                                    <li>将数据转换为其他格式</li>
                                    <li>对数据进行清洗和处理</li>
                                    <li>生成数据分析报告</li>
                                    <li>修改或优化抓取代码</li>
                                    <li>重新抓取特定内容</li>
                                </ul>
                            </div>
                        </div>
                        <form id="continueInteractionForm">
                            <div class="mb-3">
                                <label for="userQuery" class="form-label">
                                    <i class="bi bi-person-fill me-1"></i>您的问题或指示：
                                </label>
                                <textarea class="form-control" id="userQuery" rows="3"
                                    placeholder="请直接说出您的需求，AI会主动执行相关操作...&#10;例如：分析数据、转换格式、生成报告、修改代码等"></textarea>
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>
                                    提示：AI会根据您的需求自动判断并执行相应的函数调用
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success fw-bold">
                                <i class="bi bi-play-fill me-1"></i> 立即执行
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 优化后的提示词模态框 -->
    <div class="modal fade" id="optimizedPromptModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        确认优化后的提示词
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info mb-3">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        AI已根据您的任务需求优化了提示词，请确认是否满足您的需求。
                    </div>

                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <i class="bi bi-stars me-2"></i>
                            优化后的提示词内容
                        </div>
                        <div class="card-body p-0">
                            <div id="optimizedPrompt" class="bg-light rounded"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="hasOptimizedComment">
                            <label class="form-check-label" for="hasOptimizedComment">
                                <i class="bi bi-pencil-square me-1"></i>
                                我有修改意见
                            </label>
                        </div>
                    </div>
                    <div id="optimizedCommentDiv" class="mb-3 hidden">
                        <label for="optimizedComment" class="form-label">请输入您的修改意见：</label>
                        <textarea class="form-control" id="optimizedComment" rows="3" placeholder="请描述您希望如何修改优化后的提示词..."></textarea>
                        <div class="form-text">例如：请增加对数据格式的说明、减少翻页数量、添加某些特殊处理需求等</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-danger" id="rejectOptimizedPrompt">
                        <i class="bi bi-x-circle me-1"></i>
                        拒绝
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmOptimizedPrompt">
                        <i class="bi bi-check2-circle me-1"></i>
                        确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 函数调用模态框 -->
    <div class="modal fade" id="functionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-gear-fill me-2"></i>
                        确认函数调用
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info mb-3">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        AI正在请求执行以下函数，请确认是否允许执行。
                    </div>

                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <i class="bi bi-code-slash me-2"></i>
                            函数详情
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">函数名称：</label>
                                <div id="functionName" class="p-2 bg-light rounded fw-bold text-primary"></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">函数描述：</label>
                                <div id="functionDescription" class="p-2 bg-light rounded"></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">函数参数：</label>
                                <pre id="functionArgs" class="function-args"></pre>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="hasFunctionComment">
                            <label class="form-check-label" for="hasFunctionComment">
                                <i class="bi bi-pencil-square me-1"></i>
                                我有修改意见
                            </label>
                        </div>
                    </div>
                    <div id="functionCommentDiv" class="mb-3 hidden">
                        <label for="functionComment" class="form-label">请输入您的修改意见：</label>
                        <textarea class="form-control" id="functionComment" rows="3" placeholder="请描述您希望如何修改该函数调用..."></textarea>
                        <div class="form-text">例如：请修改参数值、使用其他函数、调整处理方式等</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-danger" id="rejectFunction">
                        <i class="bi bi-x-circle me-1"></i>
                        拒绝
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmFunction">
                        <i class="bi bi-check2-circle me-1"></i>
                        确认执行
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 代码执行确认模态框 -->
    <div class="modal fade" id="executeModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-code-square me-2"></i>
                        确认执行代码
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning mb-3">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        AI请求执行以下代码文件，请确认是否允许执行。
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">代码文件路径：</label>
                        <div id="codeFilePath" class="p-2 bg-light rounded fw-bold text-primary"></div>
                    </div>

                    <div class="form-text mb-3">
                        <i class="bi bi-info-circle me-1"></i>
                        执行代码将在服务器端运行，结果将返回并显示在执行进度中。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-danger" id="rejectExecution">
                        <i class="bi bi-x-circle me-1"></i>
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmExecution">
                        <i class="bi bi-play-fill me-1"></i>
                        执行
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 停止任务确认模态框 -->
    <div class="modal fade" id="stopTaskModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        确认停止任务
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning mb-3">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        您确定要停止当前正在执行的任务吗？
                    </div>

                    <div class="mb-3">
                        <p class="mb-2"><strong>停止任务将会：</strong></p>
                        <ul class="mb-0">
                            <li>立即终止当前正在执行的操作</li>
                            <li>清除任务执行状态</li>
                            <li>保留已完成的部分结果</li>
                            <li>允许您重新开始新的任务</li>
                        </ul>
                    </div>

                    <div class="form-text">
                        <i class="bi bi-lightbulb me-1"></i>
                        提示：停止后您可以修改参数重新开始任务
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>
                        取消
                    </button>
                    <button type="button" class="btn btn-warning" id="confirmStopTask">
                        <i class="bi bi-stop-fill me-1"></i>
                        确认停止
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io@4.5.4/client-dist/socket.io.min.js"></script>
    <script src="{{ url_for('static', filename='js/ui-utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/socket-handlers.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
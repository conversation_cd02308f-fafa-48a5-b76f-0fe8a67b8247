<p align="center">
  <img src="static/images/icrawler_logo.png" alt="iCrawler Logo" width="200"/>
</p>

# iCrawler - 智能数据抓取助手

iCrawler是一个基于AI技术的智能数据抓取工具，帮助用户轻松从网页获取结构化数据，无需编写复杂的爬虫代码。

## 目录结构

```
icrawler/
├── handlers.py           # 后端主逻辑，处理SocketIO事件和任务流转
├── functions.py          # 具体功能函数（采集、清洗、代码生成、执行等）
├── config.py             # 配置文件（目录、API密钥等）
├── utils.py              # 工具函数
├── templates/
│   └── index.html        # 前端主页面
├── static/               # 前端静态资源
│   ├── css/
│   ├── js/
│   └── images/
├── history.json          # 任务历史记录（自动生成）
├── requirements.txt      # Python依赖
└── README.md             # 项目说明
```

## 系统要求

- **Python 版本**: 3.11 或更高版本
- **操作系统**: Windows, macOS 或 Linux
- **内存**: 至少 4GB RAM 推荐 8GB 以上
- **存储空间**: 至少 500MB 可用空间

## 功能特点

- **多种任务类型**：支持列表详情页、搜索结果、单页数据等多种抓取场景
- **智能化处理**：通过AI自动分析网页结构并提取数据
- **自动代码生成与执行**：自动生成Playwright爬虫代码并自动执行，无需手写代码
- **多种数据格式**：支持CSV、JSON、Excel、文本等多种导出格式
- **自定义配置**：可根据需求自定义数据字段、格式和特殊处理要求
- **实时进度展示**：直观展示抓取进度和结果
- **交互式操作**：完成抓取后可继续与AI交互，进行数据分析和处理建议
- **历史记录管理**：自动保存历史操作记录，支持失败重试、同一任务只保留最新成功结果
- **任务去重**：同一任务（相同url/type/requirements）只保留一次历史，优先保留成功记录

## 安装步骤
见文档https://trip.feishu.cn/wiki/Oi9TwYrHxiFuiFk4qAucNJm7n9c
## 运行效果展示

<p align="center">
  <img src="static/images/model.png" alt="iCrawler运行效果" width="800"/>
  <br>
  <em>iCrawler 实际运行界面</em>
</p>

## 使用说明

1. **输入任务信息**：
   - 填写目标网址
   - 选择任务类型
   - 详细描述数据要求内容
   - 选择数据保存格式并配置相关选项
   - 设置是否需要翻页及翻页数量
   - 添加其他特殊要求（可选）

2. **开始抓取**：
   - 点击"开始抓取"按钮
   - 确认AI优化后的提示词
   - 需要时确认函数调用
   - 查看实时执行进度

3. **后续交互**：
   - 抓取完成后，可通过交互界面向AI提问
   - 获取数据分析建议、数据价值解释等

4. **历史任务管理**：
   - 每次任务自动记录到历史，支持失败重试
   - 同一任务（url/type/requirements相同）只保留一次历史，优先保留成功结果
   - 历史任务可在前端页面查看、复用

## 技术架构

- 前端：HTML, CSS, JavaScript, Bootstrap
- 后端：Python, Flask, Flask-SocketIO
- 通信：Socket.IO
- AI能力：OpenAI API
- 爬虫内核：Playwright

## 常见问题（FAQ）

- **Q: 采集失败如何重试？**  
  A: 只需重新提交任务，系统会自动覆盖同一任务的失败记录，只保留最新成功结果。

- **Q: 支持哪些数据格式？**  
  A: 支持 CSV、JSON、Excel、TXT 等，详见前端高级选项。

- **Q: 如何自定义采集逻辑？**  
  A: 可在任务描述中详细说明需求，或手动编辑生成的代码后再执行。

- **Q: 任务历史如何管理？**  
  A: 系统自动记录每次任务，按唯一性（url/type/requirements）去重，优先保留成功记录。

- **Q: 为什么有些网站无法采集？**  
  A: 某些需要登录或有复杂反爬机制的网站可能无法正常抓取。

## 注意事项

- 请合理使用本工具，遵守网站robots协议和相关法律法规
- 抓取大量数据时可能需要较长时间，请耐心等待
- 某些需要登录或有复杂反爬机制的网站可能无法正常抓取

## 路线图

- 优化对复杂DOM结构和异步加载页面的支持
- 增加验证码识别、插件扩展等高级功能
- 支持更多数据导出格式和存储方式

我们欢迎社区贡献，如果您有好的想法或建议，请提交Issue或Pull Request。

## 许可证

[MIT License](LICENSE) 
[{"time": "2025-06-25 10:34:20", "prompt": {"url": "https://www.virtuoso.com/hotels#CurrentPage=1&FacetCategoryIndex=0&FacetLimit=6&LeftToShow=0&RowsPerPage=25&SearchView=1col&StartRow=0&HotelBookingNumberChildren=0&HotelBookingNumberAdults=2&SearchType=Property&SortType=HotelNameAsc", "type": "单页类型", "requirements": "提取这个页面的酒店字段", "format": "", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\virtuoso_hotels.csv", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\scrape_hotels.py", "status": "已完成"}, {"time": "2025-07-08 18:41:54", "prompt": {"url": "https://www.jambase.com/festivals/all/page/2", "type": "单页类型", "requirements": "detailUrl\ttitle\timageUrl\tstartDate\tendDate\tdurationDays\tvenue\taddress\tcity\tstate\tpostalCode\tcountry\tlat\tlon\tperformers\twebsite\tsocialLinks", "format": "CSV 文件", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\jambase_festivals.csv", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\jambase_festivals_list_pages.py", "status": "已完成"}, {"time": "2025-07-23 20:33:32", "prompt": {"url": "https://www.lonelyplanet.com/search", "type": "单页类型", "requirements": "这是一个搜索页面", "format": "", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\lonelyplanet_ny.csv", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\lonelyplanet_ny.py", "status": "已完成"}, {"time": "2025-07-23 16:21:14", "prompt": {"url": "https://www.lonelyplanet.com/search?q=<PERSON>+<PERSON>", "type": "单页类型", "requirements": "这是一个搜索页面", "format": "", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\lonelyplanet_first_result.csv", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\lonelyplanet_first_result.py", "status": "已完成"}, {"time": "2025-07-23 18:46:55", "prompt": {"url": "https://eplus.jp/sf/play/2.5musical/p3", "type": "单页类型", "requirements": "访问这个页面", "format": "", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "执行失败", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\eplus_p3_spider.py", "status": "失败"}, {"time": "2025-07-23 19:18:42", "prompt": {"url": "", "type": "", "requirements": "", "format": "", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\lonelyplanet_search_results.jsonl", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\lonelyplanet_search_increment_yogyakarta.py", "status": "已完成"}, {"time": "2025-07-23 19:10:14", "prompt": {"url": "https://www.baidu.com/", "type": "单页类型", "requirements": "采集热搜信息", "format": "CSV 文件", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\baidu_hot_search.csv", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\baidu_hot_search.py", "status": "已完成"}, {"time": "2025-07-23 20:08:59", "prompt": {"url": "https://www.lonelyplanet.com/articles/best-time-to-visit-new-york-state", "type": "单页类型", "requirements": "这是一个详情页面", "format": "", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\lonelyplanet_ny.csv", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\lonelyplanet_ny_scraper.py", "status": "已完成"}, {"time": "2025-07-23 20:43:35", "prompt": {"url": "https://eplus.jp/sf/play/2.5musical/p3", "type": "单页类型", "requirements": "采集页面的活动信息", "format": "CSV 文件", "pagination": "", "additional": ""}, "code": null, "result": null, "file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\data\\eplus_events.csv", "code_file_path": "D:\\Users\\wenbochen.CN1\\PycharmProjects\\icrawler\\temp\\codes\\eplus_scraper.py", "status": "已完成"}]
from playwright.sync_api import sync_playwright
import time
import json
import re
from urllib.parse import urljoin, urlparse

class PricelessScraper:
    def __init__(self):
        self.browser = None
        self.page = None
        self.base_url = "https://www.priceless.com"
        self.data = []
        
    def start_browser(self, headless=False):
        """启动浏览器"""
        playwright = sync_playwright().start()
        self.browser = playwright.chromium.launch(headless=headless)
        self.page = self.browser.new_page()
        print("✅ 浏览器启动成功")
        
    def switch_to_english(self):
        """切换页面语言到English"""
        try:
            print("访问Priceless页面...")
            self.page.goto("https://www.priceless.com/filter/options/contentType/1", wait_until='networkidle')
            time.sleep(3)
            
            # 处理cookie
            print("处理cookie...")
            try:
                cookie_button = self.page.locator('button:has-text("接受")').first
                if cookie_button.is_visible():
                    cookie_button.click()
                    print("✅ cookie已处理")
                    time.sleep(2)
            except:
                print("ℹ️ 未找到cookie弹窗")
            
            # 第一次点击小地球
            print("=== 第一次点击小地球 ===")
            globe = self.page.locator('img[src*="globe"]').first
            if globe.is_visible():
                globe.click()
                print("✅ 第一次点击小地球成功")
                time.sleep(3)
            else:
                print("❌ 未找到小地球图标")
                return False
            
            # 寻找并点击语言选择按钮
            print("=== 寻找语言选择按钮 ===")
            language_selectors = [
                'button.current-language',
                'button:has-text("中文")',
                'button:has-text("Chinese")',
                'button[class*="language"]'
            ]
            
            lang_button = None
            for selector in language_selectors:
                try:
                    btn = self.page.locator(selector).first
                    if btn.is_visible():
                        print(f"✅ 找到语言按钮: {selector}")
                        lang_button = btn
                        break
                except:
                    continue
            
            if lang_button:
                print("点击语言选择按钮...")
                lang_button.click(force=True)
                print("✅ 语言按钮点击完成，预期退回到小地球状态...")
                time.sleep(3)
            
            # 重新点击小地球
            print("=== 重新点击小地球 ===")
            globe2 = self.page.locator('img[src*="globe"]').first
            if globe2.is_visible():
                globe2.click()
                print("✅ 重新点击小地球成功")
                time.sleep(3)
            
            # 等待语言菜单并点击English
            print("=== 点击English ===")
            try:
                self.page.wait_for_selector('#language-scroll', state='visible', timeout=5000)
                english_btn = self.page.locator('button[lang-id="1"]').first
                if english_btn.is_visible():
                    english_btn.click()
                    print("✅ English点击成功")
                    time.sleep(5)
                    
                    # 验证语言切换
                    html_lang = self.page.locator('html').get_attribute('lang')
                    if html_lang == 'en-US':
                        print("🎉 语言切换成功！")
                        return True
                    else:
                        print(f"❌ 语言切换可能失败，当前语言: {html_lang}")
                        return False
            except Exception as e:
                print(f"语言切换失败: {e}")
                return False
                
        except Exception as e:
            print(f"切换语言出错: {e}")
            return False
    
    def scrape_list_page(self, max_items=10):
        """抓取列表页的基本信息"""
        print(f"\n=== 开始抓取列表页信息（最多{max_items}项）===")
        
        try:
            # 等待页面加载完成 - 更长的等待时间
            print("等待页面加载...")
            time.sleep(5)
            
            # 截图调试
            self.page.screenshot(path="debug_list_page.png")
            print("📸 列表页截图已保存: debug_list_page.png")
            
            # 打印页面标题确认页面状态
            page_title = self.page.title()
            print(f"📄 页面标题: {page_title}")
            
            # 尝试滚动页面加载更多内容
            print("滚动页面加载内容...")
            self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            time.sleep(3)
            
            # 更精确的卡片选择器
            card_selectors = [
                # 基于您图片中看到的结构
                'article',
                '.card',
                '.experience',
                '.product',
                '[class*="experience-card"]',
                '[class*="product-card"]',
                '[class*="listing"]',
                '[class*="item"]',
                # 通用容器
                'div:has(img):has(h2)',
                'div:has(img):has(h3)',
                'div:has(img):has(a[href*="/"])',
                # 更宽泛的搜索
                'div[class]:has(img)',
                'section[class]',
                'li[class]'
            ]
            
            cards = []
            for selector in card_selectors:
                try:
                    print(f"🔍 尝试选择器: {selector}")
                    found_cards = self.page.locator(selector).all()
                    print(f"   找到 {len(found_cards)} 个元素")
                    
                    if found_cards and len(found_cards) > 0:
                        # 过滤掉明显不是卡片的元素
                        valid_cards = []
                        for card in found_cards:
                            try:
                                card_text = card.text_content().strip()
                                card_html = card.inner_html()
                                
                                # 检查是否包含有意义的内容
                                if (len(card_text) > 20 and 
                                    len(card_html) > 100 and
                                    ('img' in card_html.lower() or 'href' in card_html.lower())):
                                    valid_cards.append(card)
                                    
                            except:
                                continue
                        
                        if valid_cards:
                            print(f"✅ 找到 {len(valid_cards)} 个有效卡片，使用选择器: {selector}")
                            cards = valid_cards[:max_items]  # 限制数量
                            break
                        else:
                            print(f"   过滤后没有有效卡片")
                            
                except Exception as e:
                    print(f"   选择器出错: {e}")
                    continue
            
            if not cards:
                print("❌ 未找到任何有效卡片")
                # 尝试获取页面的主要内容区域
                print("🔍 分析页面结构...")
                try:
                    # 获取所有包含图片的div
                    img_containers = self.page.locator('div:has(img)').all()
                    print(f"找到 {len(img_containers)} 个包含图片的容器")
                    
                    # 获取所有链接
                    links = self.page.locator('a[href*="/"]').all()
                    print(f"找到 {len(links)} 个内部链接")
                    
                    # 如果有链接，尝试从链接中找到体验页面
                    experience_links = []
                    for link in links[:20]:  # 只检查前20个
                        try:
                            href = link.get_attribute('href')
                            if href and any(keyword in href for keyword in ['experience', 'product', 'travel', 'culinary', 'health']):
                                experience_links.append(link)
                        except:
                            continue
                    
                    if experience_links:
                        print(f"✅ 找到 {len(experience_links)} 个体验相关链接")
                        cards = experience_links[:max_items]
                    
                except Exception as e:
                    print(f"页面结构分析失败: {e}")
                
                if not cards:
                    return []
            
            list_data = []
            for i, card in enumerate(cards):
                try:
                    print(f"\n--- 处理第 {i+1} 个卡片 ---")
                    
                    # 提取基本信息
                    card_info = self.extract_card_info(card)
                    if card_info:
                        list_data.append(card_info)
                        print(f"✅ 卡片信息提取成功: {card_info.get('title', 'Unknown')}")
                    
                except Exception as e:
                    print(f"❌ 处理第 {i+1} 个卡片失败: {e}")
                    continue
            
            print(f"\n✅ 列表页抓取完成，共获取 {len(list_data)} 条记录")
            return list_data
            
        except Exception as e:
            print(f"❌ 抓取列表页失败: {e}")
            return []
    
    def extract_card_info(self, card):
        """从卡片元素中提取基本信息"""
        try:
            info = {}
            
            # 先获取卡片的HTML内容用于调试
            try:
                card_html = card.inner_html()
                print(f"🔍 卡片HTML片段: {card_html[:200]}...")
            except:
                print("🔍 无法获取卡片HTML")
            
            # 提取标题 - 使用更广泛的选择器
            title_selectors = [
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                '.title', '[class*="title"]', 
                'a[href*="/"]',  # 包含链接的文本
                '[class*="name"]',
                '[class*="heading"]',
                'p', 'span', 'div'  # 作为后备选项
            ]
            
            for selector in title_selectors:
                try:
                    title_elems = card.locator(selector).all()
                    for elem in title_elems:
                        if elem.is_visible():
                            title = elem.text_content().strip()
                            if title and len(title) > 5 and len(title) < 200:  # 合理的标题长度
                                # 过滤掉明显不是标题的文本
                                if not any(skip in title.lower() for skip in ['¥', '$', 'per', 'booking', 'adult']):
                                    info['title'] = title
                                    print(f"✅ 找到标题: {title}")
                                    break
                    if info.get('title'):
                        break
                except Exception as e:
                    print(f"提取标题时出错 ({selector}): {e}")
                    continue
            
            # 提取价格 - 更精确的价格匹配
            price_patterns = [
                r'¥\d+[,\d]*',
                r'\$\d+[,\d]*', 
                r'€\d+[,\d]*',
                r'£\d+[,\d]*',
                r'\d+[,\d]*\s*per\s+\w+',
                r'from\s+¥\d+',
                r'from\s+\$\d+'
            ]
            
            # 在整个卡片文本中搜索价格
            try:
                card_text = card.text_content()
                for pattern in price_patterns:
                    matches = re.findall(pattern, card_text, re.IGNORECASE)
                    if matches:
                        info['price'] = matches[0].strip()
                        print(f"✅ 找到价格: {matches[0]}")
                        break
            except Exception as e:
                print(f"提取价格时出错: {e}")
            
            # 提取位置 - 寻找常见城市名
            location_patterns = [
                r'Beijing[^,]*',
                r'Paris[^,]*', 
                r'London[^,]*',
                r'New York[^,]*',
                r'Tokyo[^,]*',
                r'Shanghai[^,]*',
                r'Hong Kong[^,]*',
                r'\w+,\s*\w+',  # 城市,国家格式
            ]
            
            try:
                card_text = card.text_content()
                for pattern in location_patterns:
                    matches = re.findall(pattern, card_text, re.IGNORECASE)
                    if matches:
                        info['location'] = matches[0].strip()
                        print(f"✅ 找到位置: {matches[0]}")
                        break
            except Exception as e:
                print(f"提取位置时出错: {e}")
            
            # 提取链接 - 寻找详情页链接
            try:
                # 寻找包含链接的元素
                link_elems = card.locator('a[href]').all()
                for elem in link_elems:
                    href = elem.get_attribute('href')
                    if href and ('product' in href or 'experience' in href or len(href) > 10):
                        if href.startswith('/'):
                            href = urljoin(self.base_url, href)
                        info['detail_url'] = href
                        print(f"✅ 找到链接: {href}")
                        break
            except Exception as e:
                print(f"提取链接时出错: {e}")
            
            # 提取图片
            try:
                img_elems = card.locator('img').all()
                for elem in img_elems:
                    img_src = elem.get_attribute('src')
                    if img_src and not img_src.endswith('.svg'):  # 排除图标
                        if img_src.startswith('/'):
                            img_src = urljoin(self.base_url, img_src)
                        info['image_url'] = img_src
                        print(f"✅ 找到图片: {img_src}")
                        break
            except Exception as e:
                print(f"提取图片时出错: {e}")
            
            # 打印提取到的所有信息
            print(f"📋 卡片信息汇总: {info}")
            
            # 确保至少有一些有用信息
            if info.get('title') or info.get('detail_url') or info.get('price'):
                return info
            else:
                print("❌ 未提取到足够的有用信息")
                return None
                
        except Exception as e:
            print(f"❌ 提取卡片信息失败: {e}")
            return None
    
    def scrape_detail_page(self, detail_url):
        """抓取详情页信息"""
        try:
            print(f"\n=== 访问详情页: {detail_url} ===")
            
            # 访问详情页
            self.page.goto(detail_url, wait_until='networkidle')
            time.sleep(3)
            
            detail_info = {}
            
            # 提取详细描述
            description_selectors = [
                '.experience-description',
                '[class*="description"]',
                '.content',
                'p:has-text("Board")',
                'p:has-text("Experience")',
                'div:has-text("The Experience")'
            ]
            
            for selector in description_selectors:
                try:
                    desc_elem = self.page.locator(selector).first
                    if desc_elem.is_visible():
                        description = desc_elem.text_content().strip()
                        if description and len(description) > 50:
                            detail_info['description'] = description
                            break
                except:
                    continue
            
            # 提取亮点信息
            try:
                highlights = []
                highlight_selectors = [
                    '.highlights li',
                    '[class*="highlight"] li',
                    'ul li:has-text("Sail")',
                    'ul li:has-text("Dine")',
                    'ul li:has-text("Skip")'
                ]
                
                for selector in highlight_selectors:
                    try:
                        highlight_elems = self.page.locator(selector).all()
                        if highlight_elems:
                            for elem in highlight_elems:
                                text = elem.text_content().strip()
                                if text and len(text) > 10:
                                    highlights.append(text)
                            if highlights:
                                break
                    except:
                        continue
                
                if highlights:
                    detail_info['highlights'] = highlights
            except:
                pass
            
            # 提取价格信息
            price_selectors = [
                '[class*="price"]',
                'text=/¥\\d+/',
                'text=/\\$\\d+/',
                '.cost'
            ]
            
            for selector in price_selectors:
                try:
                    price_elem = self.page.locator(selector).first
                    if price_elem.is_visible():
                        price = price_elem.text_content().strip()
                        if price:
                            detail_info['detailed_price'] = price
                            break
                except:
                    continue
            
            # 提取可用日期
            try:
                dates = []
                date_selectors = [
                    '.available-dates td',
                    '[class*="date"]',
                    'text=/July|August|December/',
                    'text=/\\d{1,2}\\s+(July|August|December)/'
                ]
                
                for selector in date_selectors:
                    try:
                        date_elems = self.page.locator(selector).all()
                        if date_elems:
                            for elem in date_elems:
                                date_text = elem.text_content().strip()
                                if date_text and ('July' in date_text or 'August' in date_text or 'December' in date_text):
                                    dates.append(date_text)
                            if dates:
                                break
                    except:
                        continue
                
                if dates:
                    detail_info['available_dates'] = dates
            except:
                pass
            
            # 提取最大人数
            try:
                people_selectors = [
                    'text=/\\d+\\s+people/',
                    'text=/group of \\d+/',
                    '[class*="people"]'
                ]
                
                for selector in people_selectors:
                    try:
                        people_elem = self.page.locator(selector).first
                        if people_elem.is_visible():
                            people_text = people_elem.text_content().strip()
                            # 提取数字
                            numbers = re.findall(r'\d+', people_text)
                            if numbers:
                                detail_info['max_people'] = int(numbers[0])
                                break
                    except:
                        continue
            except:
                pass
            
            # 提取地点信息
            location_selectors = [
                '[class*="location"]',
                '[class*="venue"]',
                'text=/Paris|Beijing|London/',
                '.city'
            ]
            
            for selector in location_selectors:
                try:
                    location_elem = self.page.locator(selector).first
                    if location_elem.is_visible():
                        location = location_elem.text_content().strip()
                        if location:
                            detail_info['detailed_location'] = location
                            break
                except:
                    continue
            
            print(f"✅ 详情页信息提取完成，获取 {len(detail_info)} 个字段")
            return detail_info
            
        except Exception as e:
            print(f"❌ 抓取详情页失败: {e}")
            return {}
    
    def run_complete_scrape(self, max_items=5):
        """运行完整的抓取流程"""
        try:
            print("🚀 开始完整的Priceless数据抓取")
            
            # 1. 启动浏览器
            self.start_browser(headless=False)
            
            # 2. 切换到English
            if not self.switch_to_english():
                print("❌ 语言切换失败，终止抓取")
                return
            
            # 3. 抓取列表页
            list_data = self.scrape_list_page(max_items)
            if not list_data:
                print("❌ 列表页抓取失败，终止抓取")
                return
            
            # 4. 遍历每个项目，抓取详情页
            complete_data = []
            for i, item in enumerate(list_data):
                print(f"\n{'='*50}")
                print(f"处理第 {i+1}/{len(list_data)} 个项目")
                print(f"{'='*50}")
                
                if item.get('detail_url'):
                    # 抓取详情页
                    detail_info = self.scrape_detail_page(item['detail_url'])
                    
                    # 合并列表页和详情页信息
                    complete_item = {**item, **detail_info}
                    complete_data.append(complete_item)
                    
                    print(f"✅ 项目 {i+1} 处理完成")
                    
                    # 避免请求过快
                    time.sleep(2)
                else:
                    print(f"⚠️ 项目 {i+1} 没有详情页链接，跳过")
                    complete_data.append(item)
            
            # 5. 保存数据
            self.save_data(complete_data)
            
            print(f"\n🎉 抓取完成！共获取 {len(complete_data)} 条完整数据")
            return complete_data
            
        except Exception as e:
            print(f"❌ 完整抓取流程失败: {e}")
            return []
        finally:
            if self.browser:
                self.browser.close()
                print("✅ 浏览器已关闭")
    
    def save_data(self, data):
        """保存数据到JSON文件"""
        try:
            filename = f"priceless_data_{int(time.time())}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 数据已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")

def main():
    """主函数"""
    scraper = PricelessScraper()
    
    # 运行完整抓取，最多抓取5个项目
    data = scraper.run_complete_scrape(max_items=5)
    
    if data:
        print(f"\n📊 抓取结果概览:")
        for i, item in enumerate(data, 1):
            print(f"{i}. {item.get('title', 'Unknown Title')}")
            print(f"   价格: {item.get('price', 'N/A')} / {item.get('detailed_price', 'N/A')}")
            print(f"   位置: {item.get('location', 'N/A')} / {item.get('detailed_location', 'N/A')}")
            print(f"   详情页: {'✅' if item.get('description') else '❌'}")
            print()

if __name__ == "__main__":
    main() 
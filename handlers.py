import json
from flask import request
from flask_socketio import emit
import utils
from config import message_history, execution_locks, function_execution_locks, task_cancellation_flags, lock, is_first_input, DEFAULT_MODEL_NAME,PROMPT_MODEL_NAME
import os
import re
from functions import save_history_record
import config

# 函数名到自然语言描述的映射
STEP_DESCRIPTIONS = {
    "open_and_save_page": "网页采集完成",
    "open_and_save_page_with_remote_browser": "网页采集完成",
    "clean_html": "数据清洗完成",
    "get_playwright_code": "代码生成完成",
    "execute_playwright_code": "代码执行完成",
    "edit_code": "代码保存完成",
    "list_directory_contents": "目录检索完成",
    "read_file_content": "文件读取完成",
    "check_file_exists": "文件检查完成",
    "delete_files": "文件删除完成"
}


def check_task_cancellation(sid):
    """检查任务是否被取消"""
    return task_cancellation_flags.get(sid, False)


def handle_stop_task(data, socketio):
    """处理停止任务请求"""
    from flask import request

    sid = request.sid
    print(f"收到停止任务请求，会话ID: {sid}")

    try:
        with lock:
            # 设置取消标志
            task_cancellation_flags[sid] = True

            # 清除执行锁状态
            if sid in execution_locks:
                execution_locks[sid] = False
                print(f"已清除会话 {sid} 的执行锁")

            if sid in function_execution_locks:
                function_execution_locks[sid] = False
                print(f"已清除会话 {sid} 的函数执行锁")

        # 发送任务停止确认
        socketio.emit('task_stopped', {
            'message': '✋ 任务已停止',
            'details': '当前执行的任务已被用户手动停止'
        })

        print(f"任务停止成功，会话ID: {sid}")

    except Exception as e:
        error_msg = f'停止任务时出错: {str(e)}'
        print(error_msg)
        socketio.emit('error', {'message': error_msg})


def handle_start_automation(data, socketio):
    global is_first_input
    is_continue = data.get('is_continue', False)

    # 重置取消标志，开始新任务
    with lock:
        task_cancellation_flags[request.sid] = False

    # 只有不是继续交互时才清空历史
    if not is_continue and request.sid in message_history:
        message_history[request.sid] = []
    prompt = data.get('prompt')
    if not prompt:
        emit('error', {'message': '请提供有效的提示词'})
        return

    try:
        # 提示词预处理
        prompt = prompt.strip()
        if not prompt:
            emit('error', {'message': '提示词不能为空'})
            return

        # 获取当前会话的消息历史
        messages = message_history.get(request.sid, [])
        # 初始化消息历史，但不立即处理

        # 只在第一次输入时初始化消息历史和优化提示词
        if is_first_input:
            messages = [
                {"role": "system", "content": "你是一个帮助用户自动化任务的助手"},
            ]
            socketio.emit('progress_update', {'message': '正在优化用户输入...'})
            init_content = f"""
                    原始 prompt：\n\n{prompt}，
                    请优化原始prompt，确保其清晰、准确，并提高效率。要求：
                    1. 确保结构清晰、简洁。如果有不理解的,询问用户,不可以编造
                    2. 提供优化后的分条执行步骤；
                    3. 如果有必要，拆解复杂的部分，并给出简洁的替代方案；
                    4. 注意指明每个步骤需要调用的函数
                    5. 如果函数调用过程中出现错误,优先自动重试,如果重试失败可以调用其他相似的函数
                    6. 如果没提到爬取详情页信息，不需要爬取详情页，只需要从列表页找到信息和翻页逻辑,生成代码需要充分考虑到网络不好的情况
                    重要:
                    1.关于访问页面:如果是多个类似的页面,只需要访问一个代表性页面,用代表性页面生成代码，下面是例子:1.任务是访问某个页面并翻3页。只需要用第一页生成代码找到翻页逻辑并翻页。2.任务是访问某个列表页，并且需要访问详情页。只需要访问当前列表页和当前列表页的第一个详情页，从当前列表页找到翻页逻辑和信息抓取逻辑。
                    2.关于访问页面的方法:对于中国站点，优先使用远程浏览器访问，对于外国站点，优先使用本地浏览器访问,headless=false
                    3.关于生成代码函数:get_playwright_code必须包含下列参数:clean_html_path_list (str): 需要清洗之后的html路径，多个路径用逗号分隔,model_response (str): 大模型的响应内容, code_file_path (str): python文件的名称,不包含目录名称，model_response参数必须包含完整的url
                    4.关于搜索任务,每一个搜索结果只取一条链接,这个链接应该是该结果的主要链接,默认使用https://www.google.com.hk作为搜索引擎首页
                    5.关于结果,每一个任务都需要生成python文件,保存结果到本地
                    6.关于url,如果用户输入的url不完整或者是指代url的文字,应该默认转化为完整url
                    7.关于翻页:如果用户不指定翻页，那就不要有翻页逻辑。如果指定翻页，翻页逻辑只需要从一个代表性页面找到
                    
            """
            messages.append({"role": "user", "content": init_content})
            message_history[request.sid] = messages
            optimized_prompt = utils.optimize_prompt(PROMPT_MODEL_NAME, messages)
            socketio.emit('progress_update', {'message': '输入优化完成,即将开始执行任务'})

            # 新增：推送优化后的提示词到前端
            socketio.emit('optimized_prompt', {
                'optimized_prompt': optimized_prompt
            }, to=request.sid)

            # 直接使用优化后的提示词继续流程，不弹窗、不等待用户确认
            messages.append({"role": "user", "content": f"上一次模型生成的提示词为{optimized_prompt}"})
            messages.append(
                {"role": "user", "content": f"请指示下一步动作,如果需要调用function,必须使用function_call来调用"})
            is_first_input = False
            # 直接进入后续流程
            process_automation(optimized_prompt, messages, socketio)

        else:
            # 非首次输入，直接使用原始提示词，并添加到现有消息历史
            optimized_prompt = prompt
            messages.append({
                "role": "user",
                "content": f"新的用户输入: {prompt}"
            })

            # 继续处理流程
            process_automation(optimized_prompt, messages, socketio)

        # 保存更新后的消息历史
        message_history[request.sid] = messages

    except Exception as e:
        emit('error', {'message': f'发生错误: {str(e)}'})


def handle_delete_message(data, socketio):
    try:
        index = data.get('index')
        if index is not None and request.sid in message_history:
            # 确保索引在有效范围内
            if 0 <= index < len(message_history[request.sid]):
                # 删除消息
                del message_history[request.sid][index]
                socketio.emit('message_deleted', {'index': index})
    except Exception as e:
        socketio.emit('error', {'message': f'删除消息时发生错误: {str(e)}'})


def send_result_with_file_content(socketio, result_message):
    from functions import extract_file_path_by_ai
    file_path = extract_file_path_by_ai(result_message)
    if file_path and os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            socketio.emit('show_result', {
                'result': f"【文件内容预览】\n{content[:5000]}",
                'file_path': file_path
            })
            return
        except Exception as e:
            socketio.emit('show_result', {
                'result': f"{result_message}\n\n[读取文件内容失败: {e}]",
                'file_path': file_path
            })
            return
    # 默认行为
    socketio.emit('show_result', {'result': result_message, 'file_path': None})


#
def handle_confirm_execution(data, socketio):
    from functions import execute_playwright_code

    confirmed = data.get('confirmed', False)
    code_file_path = data.get('code_file_path')

    if not code_file_path:
        socketio.emit('error', {'message': '❌ 未找到代码文件路径'})
        return

    if confirmed:
        # 执行代码
        execute_result = execute_playwright_code(code_file_path)
        # 展示执行结果（带内容）
        send_result_with_file_content(socketio, execute_result.get('message', ''))
        # 精简进度栏提示
        desc = STEP_DESCRIPTIONS.get('execute_playwright_code', '任务完成')
        socketio.emit('progress_update', {
            'message': f'✅ {desc}'
        })
        socketio.emit('complete', {'message': execute_result.get('message', '代码执行完成')})
    else:
        socketio.emit('complete', {'message': '已取消代码执行'})


def process_automation(prompt, messages, socketio):
    from config import functions

    try:
        # 检查是否正在处理
        sid = request.sid
        with lock:
            if sid in execution_locks and execution_locks[sid]:
                socketio.emit('progress_update', {
                    'message': '当前有任务正在执行，请等待完成...'
                })
                return
            execution_locks[sid] = True

        try:
            # 检查任务是否被取消
            if check_task_cancellation(sid):
                socketio.emit('task_stopped', {
                    'message': '✋ 任务已停止',
                    'details': '任务在执行过程中被用户取消'
                })
                return

            # 1. 获取 OpenAI 响应
            completion = utils.client.chat.completions.create(
                model=DEFAULT_MODEL_NAME,
                messages=messages,
                functions=functions,
                function_call="auto"
            )

            response = completion.choices[0].message
            if response.function_call:
                function_name = response.function_call.name
                function_args = json.loads(response.function_call.arguments)

                # 发送函数调用信息给前端确认 - 前端会根据autoConfirmFunction设置决定是自动确认还是显示确认对话框
                socketio.emit('function_call', {
                    'function_name': function_name,
                    'function_args': function_args,
                    'description': response.content
                })

                # 更新消息历史
                message_history[request.sid] = messages

            else:
                # 如果没有函数调用，直接发送响应内容
                socketio.emit('complete', {'message': response.content})
        finally:
            # 清除处理状态
            with lock:
                execution_locks[sid] = False

    except Exception as e:
        socketio.emit('error', {'message': f'自动化过程出错: {str(e)}'})
        # 确保在发生错误时也清除锁
        with lock:
            execution_locks[request.sid] = False


def handle_confirm_function(data, socketio):
    try:
        function_name = data.get('function_name')
        function_args = data.get('function_args')

        # 兼容性修复：检查参数结构并进行修正
        if function_args is None and 'args' in data:
            function_args = data.get('args')
            print(f"从'args'字段中获取函数参数: {function_args}")

        user_comment = data.get('user_comment')
        has_comment = data.get('has_comment', False)

        sid = request.sid

        # 记录接收到的数据
        print(f"接收到的确认函数数据: {data}")
        print(f"解析得到的函数名: {function_name}")
        print(f"解析得到的函数参数: {function_args}")

        # 检查函数是否正在执行
        with lock:
            if sid in function_execution_locks and function_execution_locks[sid]:
                socketio.emit('progress_update', {
                    'message': f'函数 {function_name} 正在执行中，请等待完成...'
                })
                return
            function_execution_locks[sid] = True

        try:
            # 检查任务是否被取消
            if check_task_cancellation(sid):
                socketio.emit('task_stopped', {
                    'message': '✋ 任务已停止',
                    'details': '任务在函数执行前被用户取消'
                })
                return

            print(f"收到确认函数请求: {function_name}")
            print(f"函数参数: {function_args}")
            print(f"是否有用户意见: {has_comment}")

            # 更新消息历史
            if sid in message_history:
                messages = message_history[sid]

                # 只有在有用户意见时才添加意见并重新生成建议
                if has_comment and user_comment:
                    messages.append({
                        "role": "user",
                        "content": f"用户意见：{user_comment}\n\n请指示下一步动作，如果需要调用函数，必须使用 function_call 方法调用。"
                    })
                    message_history[sid] = messages

                    # 发送进度消息
                    socketio.emit('progress_update', {
                        'message': '正在根据您的意见重新生成建议...',
                        'next_task': '等待确认',
                        'wait_time': 3
                    })

                    # 调用process_automation函数，根据用户意见重新生成建议
                    process_automation(user_comment, messages, socketio)
                else:
                    # 直接执行函数
                    try:
                        # 在执行函数前再次检查取消状态
                        if check_task_cancellation(sid):
                            socketio.emit('task_stopped', {
                                'message': '✋ 任务已停止',
                                'details': '任务在函数执行前被用户取消'
                            })
                            return

                        print(f"开始执行函数: {function_name}")
                        result = utils.execute_function(function_name, function_args)

                        # 根据函数返回类型处理结果
                        if isinstance(result, str):
                            result_message = result
                        else:
                            result_message = result.get('message', '执行成功')
                            # 展示生成代码
                            if "python_code" in result:
                                python_code = result.get("python_code")
                                socketio.emit('show_code', {'code': python_code})
                            # 展示执行结果（带内容）
                            if "output" in result:
                                send_result_with_file_content(socketio, result.get('output', ''))
                            # 只在主流程函数时保存历史
                            MAIN_FUNCTIONS = {"get_playwright_code", "execute_playwright_code"}
                            if function_name in MAIN_FUNCTIONS:
                                # 只在 execute_playwright_code 执行完成后保存历史记录
                                if function_name == "execute_playwright_code":
                                    # debug print（无论如何都打印）
                                    print("[DEBUG] result:", result)
                                    result_file_path = result.get('save_location', None) or result.get('file_path',
                                                                                                       None)
                                    print("[DEBUG] file_path:", result_file_path)
                                    code_file_path = result.get('save_path', None) or result.get('code_file_path', None)
                                    print("[DEBUG] code_file_path:", code_file_path)
                                    status = result.get('status', None)
                                    print("[DEBUG] status:", result.get('status', None))
                                    # 保存历史记录
                                    raw_prompt = get_raw_user_prompt(messages)
                                    prompt_dict = parse_prompt_to_dict(raw_prompt)
                                    save_history_record(
                                        prompt=prompt_dict,
                                        code=result.get("python_code"),
                                        file_path=result_file_path,
                                        code_file_path=code_file_path,
                                        status='已完成' if status else '失败'
                                    )
                                    print("[DEBUG] save_history_record called.")

                        print(f"函数执行结果: {result_message}")
                        # 更新消息历史
                        messages.append({"role": "function", "name": function_name, "content": result_message})

                        # 添加用户消息，明确指示下一步操作
                        next_step_message = f"当前状态：{function_name} 函数执行完成\n执行结果：{result_message}\n\n请指示下一步动作，如果需要调用函数，必须使用 function_call 方法调用。"
                        messages.append({
                            "role": "user",
                            "content": next_step_message
                        })

                        message_history[sid] = messages

                        # 发送执行结果
                        desc = STEP_DESCRIPTIONS.get(function_name, '任务完成')
                        socketio.emit('progress_update', {
                            'message': f'✅ {desc}'
                        })

                        # 继续处理，让模型重新生成建议
                        process_automation(next_step_message, messages, socketio)

                    except Exception as e:
                        error_msg = f'执行函数 {function_name} 时出错: {str(e)}'
                        print(error_msg)
                        socketio.emit('error', {'message': error_msg})

                        # 添加错误信息到消息历史
                        if sid in message_history:
                            messages = message_history[sid]
                            # 添加函数执行错误消息
                            messages.append({
                                "role": "function",
                                "name": function_name,
                                "content": f"执行出错: {str(e)}"
                            })

                            # 添加用户消息，要求处理错误并继续
                            error_handling_message = f"函数 {function_name} 执行失败，错误信息: {str(e)}\n\n请分析错误原因并提供解决方案，然后继续执行。如需调用函数，请使用 function_call 方法。"
                            messages.append({
                                "role": "user",
                                "content": error_handling_message
                            })

                            message_history[sid] = messages

                            # 继续处理，让模型处理错误并给出后续建议
                            socketio.emit('progress_update', {
                                'message': f'正在分析错误并继续执行...'
                            })
                            process_automation(error_handling_message, messages, socketio)
        finally:
            # 清除函数执行状态
            with lock:
                function_execution_locks[sid] = False

    except Exception as e:
        error_msg = f'处理用户意见时出错: {str(e)}'
        print(error_msg)
        socketio.emit('error', {'message': error_msg})
        # 确保在发生错误时也清除锁
        with lock:
            if sid in function_execution_locks:
                function_execution_locks[sid] = False


def handle_execute_function(data, socketio):
    try:
        function_name = data.get('function_name')
        function_args = data.get('function_args')

        # 发送进度消息
        socketio.emit('progress_update', {
            'message': f'正在执行函数：{function_name}...',
            'next_task': '执行函数',
            'wait_time': 0
        })

        # 执行函数
        result = utils.execute_function(function_name, function_args)

        # 更新消息历史
        if request.sid in message_history:
            messages = message_history[request.sid]
            # 根据函数返回类型处理结果
            if isinstance(result, str):
                result_message = result
            else:
                result_message = result.get('message', '执行成功')

            # 添加函数执行结果到消息历史
            messages.append({
                "role": "function",
                "name": function_name,
                "content": result_message
            })

            # 添加用户消息，明确指示下一步操作
            next_step_message = f"当前状态：{function_name} 函数执行完成\n执行结果：{result_message}\n\n请指示下一步动作，如果需要调用函数，必须使用 function_call 方法调用。"
            messages.append({
                "role": "user",
                "content": next_step_message
            })

            message_history[request.sid] = messages

            # 发送执行结果
            desc = STEP_DESCRIPTIONS.get(function_name, '任务完成')
            socketio.emit('progress_update', {
                'message': f'✅ {desc}'
            })

            # 继续处理，让模型重新生成建议
            process_automation(next_step_message, messages, socketio)

    except Exception as e:
        error_msg = f'执行函数时出错: {str(e)}'
        print(error_msg)  # 打印错误信息到控制台
        socketio.emit('error', {'message': error_msg})


def handle_reject_function(data, socketio):
    try:
        function_name = data.get('function_name')
        user_comment = data.get('user_comment')

        # 更新消息历史，添加用户拒绝的原因
        if request.sid in message_history:
            messages = message_history[request.sid]
            messages.append({
                "role": "user",
                "content": f"拒绝执行 {function_name} 函数。原因：{user_comment}"
            })
            message_history[request.sid] = messages

            # 发送拒绝消息
            socketio.emit('progress_update', {
                'message': f'❌ 已拒绝执行：{function_name}\n原因：{user_comment}'
            })

            # 继续处理，让模型重新生成建议
            process_automation(messages[-1]["content"], messages, socketio)

    except Exception as e:
        socketio.emit('error', {'message': f'处理拒绝时出错: {str(e)}'})


def handle_confirm_optimized_prompt(data, socketio):
    try:
        optimized_prompt = data.get('optimized_prompt')
        user_comment = data.get('user_comment')
        has_comment = data.get('has_comment', False)

        # 更新消息历史
        if request.sid in message_history:
            messages = message_history[request.sid]

            # 如果有用户意见，添加到消息历史
            if has_comment and user_comment:
                messages.append({
                    "role": "user",
                    "content": f"用户对优化后的提示词的意见：{user_comment}\n\n请根据用户意见重新优化提示词。"
                })
                message_history[request.sid] = messages

                # 发送进度消息
                socketio.emit('progress_update', {
                    'message': '正在根据您的意见重新优化提示词...',
                    'next_task': '优化提示词',
                    'wait_time': 3
                })

                # 重新优化提示词
                # new_optimized_prompt = optimize_prompt("859-gpt-4o__2024-11-20", messages)
                new_optimized_prompt = utils.optimize_prompt(DEFAULT_MODEL_NAME, messages)
                # new_optimized_prompt = optimize_prompt("gpt-4o-mini", messages)
                messages.append({"role": "user", "content": f"上一次模型生成的提示词为{new_optimized_prompt}"})

                # 发送新的优化结果
                socketio.emit('optimized_prompt', {
                    'optimized_prompt': new_optimized_prompt
                })
            else:
                # 用户同意优化后的提示词，继续处理
                messages.append({
                    "role": "user",
                    "content": f"请使用优化后的prompt: {optimized_prompt}"
                })
                message_history[request.sid] = messages

                # 发送进度消息
                socketio.emit('progress_update', {
                    'message': '正在处理优化后的提示词...',
                    'next_task': '处理提示词',
                    'wait_time': 3
                })

                # 继续处理流程
                process_automation(optimized_prompt, messages, socketio)

    except Exception as e:
        socketio.emit('error', {'message': f'处理提示词确认时出错: {str(e)}'})


def get_user_prompt_from_messages(messages):
    """
    从消息历史中提取用户真实输入的任务描述。
    优先找以 '新的用户输入:' 开头的 user 消息，否则找第一个 user 消息，否则兜底 system。
    """
    if not messages:
        return ''
    # 优先找"新的用户输入:"
    for msg in reversed(messages):
        if msg.get('role') == 'user' and msg.get('content', '').strip().startswith('新的用户输入:'):
            # 去掉前缀
            return msg['content'].strip().replace('新的用户输入:', '').strip()
    # 否则找第一个 user
    for msg in messages:
        if msg.get('role') == 'user':
            return msg['content'].strip()
    # 否则兜底 system
    for msg in messages:
        if msg.get('role') == 'system':
            return msg['content'].strip()
    return ''


# 新增：获取用户原始输入 prompt（不带优化指令）
def get_raw_user_prompt(messages):
    """
    从消息历史中提取用户原始输入的 prompt（不带优化指令和系统提示）。
    优先找"新的用户输入: ..."，否则找第一个 user，去掉前缀。
    """
    if not messages:
        return ''
    for msg in reversed(messages):
        if msg.get('role') == 'user' and msg.get('content', '').strip().startswith('新的用户输入:'):
            # 只取冒号后面部分
            content = msg['content'].strip()
            idx = content.find('新的用户输入:')
            if idx != -1:
                return content[idx + 7:].strip()
            return content
    # 否则找第一个 user
    for msg in messages:
        if msg.get('role') == 'user':
            content = msg['content'].strip()
            # 若有"原始 prompt："，只取冒号后面部分
            idx = content.find('原始 prompt：')
            if idx != -1:
                # 取第一个句号或换行前
                after = content[idx + 7:].strip()
                # 截断到第一个换行或逗号
                for sep in ['，', '\n', '\r', '.']:
                    sidx = after.find(sep)
                    if sidx != -1:
                        return after[:sidx].strip()
                return after
            return content
    return ''


# 新增：解析原始 prompt 为结构化 dict
def parse_prompt_to_dict(prompt_str):
    result = {
        'url': '',
        'type': '',
        'requirements': '',
        'format': '',
        'pagination': '',
        'additional': ''
    }
    if not prompt_str:
        return result
    import re
    url = re.search(r'目标网址：([^。；;\n]+)', prompt_str)
    if url:
        result['url'] = url.group(1).strip()
    type_ = re.search(r'任务类型：([^。；;\n]+)', prompt_str)
    if type_:
        result['type'] = type_.group(1).strip()
    req = re.search(r'数据要求：([^。；;\n]+)', prompt_str)
    if req:
        result['requirements'] = req.group(1).strip()
    fmt = re.search(r'数据保存格式：([^。；;\n]+)', prompt_str)
    if fmt:
        result['format'] = fmt.group(1).strip()
    pag = re.search(r'(需要翻页，页数：[^。；;\n]+)', prompt_str)
    if pag:
        result['pagination'] = pag.group(1).strip()
    add = re.search(r'其他要求：([^。；;\n]+)', prompt_str)
    if add:
        result['additional'] = add.group(1).strip()
    return result


def handle_clear_all_messages(socketio):
    try:
        global is_first_input
        if request.sid in message_history:
            message_history[request.sid] = []
            socketio.emit('messages_cleared')
            is_first_input = True
    except Exception as e:
        socketio.emit('error', {'message': f'清空消息时发生错误: {str(e)}'})


def handle_reject_optimized_prompt(data, socketio):
    try:
        user_comment = data.get('user_comment')

        # 更新消息历史
        if request.sid in message_history:
            messages = message_history[request.sid]
            messages.append({
                "role": "user",
                "content": f"用户拒绝优化后的提示词。原因：{user_comment}\n\n流程已终止。"
            })
            message_history[request.sid] = messages

            # 发送进度消息
            socketio.emit('progress_update', {
                'message': '已终止流程',
                'next_task': '无',
                'wait_time': 0
            })

            # 发送完成消息，告知用户流程已终止
            socketio.emit('complete', {
                'message': '您已拒绝优化后的提示词，流程已终止。如需继续，请重新输入提示词。'
            })

    except Exception as e:
        socketio.emit('error', {'message': f'处理提示词拒绝时出错: {str(e)}'})

import os
import threading
import logging
from datetime import datetime

# 创建日志目录
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 设置日志配置
log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

# 创建logger实例
logger = logging.getLogger("app")

# 初始化执行状态锁
execution_locks = {}
function_execution_locks = {}
lock = threading.Lock()

# 用于跟踪任务取消状态
task_cancellation_flags = {}

# 用于跟踪是否是第一次输入
is_first_input = True

# 用于存储消息历史
message_history = {}
trip_baseUrl = "http://proxy.llm.azure.sys.ctripcorp.com"
trip_apikey = "sk-lUKVweLMlj05Svis50qeDQ"
# API配置 - 从环境变量中读取，如果不存在则需要用户配置
# 请在https://emoss.ops.ctripcorp.com/申请模型并获取API基础URL和密钥
trip_baseUrl = os.environ.get("TRIP_BASE_URL", trip_baseUrl)
trip_apikey = os.environ.get("TRIP_API_KEY", trip_apikey)

# 记录API配置信息
logger.info(f"API基础URL: {'已设置' if trip_baseUrl != 'YOUR_API_BASE_URL' else '未设置'}")
logger.info(f"API密钥: {'已设置' if trip_apikey != 'YOUR_API_KEY' else '未设置'}")

# 目录定义
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TEMP_DIR = os.path.join(BASE_DIR, "temp")
HTML_DIR = os.path.join(TEMP_DIR, "html")
CODE_DIR = os.path.join(TEMP_DIR, "codes")  # 保持与原来一致的目录名
DATA_DIR = os.path.join(TEMP_DIR, "data")

# 确保目录存在
for directory in [TEMP_DIR, HTML_DIR, CODE_DIR, DATA_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

# 浏览器可执行文件路径配置 - 使用配置管理器
from browser_config import get_browser_executable_path, BrowserConfigManager

# 创建配置管理器实例，指定配置文件在项目根目录
browser_config_file = os.path.join(BASE_DIR, "browser_config.json")
browser_config_manager = BrowserConfigManager(browser_config_file)

# 获取浏览器可执行文件路径，优先从JSON配置文件读取，否则使用配置管理器
json_browser_path = browser_config_manager.config.get("browser_executable_path")
if json_browser_path and os.path.exists(json_browser_path):
    # 如果JSON配置文件中已经设置了有效路径，就使用配置文件的值
    BROWSER_EXECUTABLE_PATH = json_browser_path
    logger.info(f"使用JSON配置文件中的浏览器路径: {BROWSER_EXECUTABLE_PATH}")
else:
    # 否则使用配置管理器获取路径（会自动检测并提示用户选择）
    BROWSER_EXECUTABLE_PATH = browser_config_manager.get_browser_path()
    logger.info(f"使用配置管理器获取的浏览器路径: {BROWSER_EXECUTABLE_PATH}")

# 新增：默认大模型名称配置
PROMPT_MODEL_NAME = os.environ.get('DEFAULT_MODEL_NAME', '859-o3__2025-04-16')

DEFAULT_MODEL_NAME = os.environ.get('DEFAULT_MODEL_NAME', '859-gpt-4_1__2025-04-14')
# DEFAULT_MODEL_NAME = os.environ.get('DEFAULT_MODEL_NAME', '859-o3-mini__2025-01-31')
GENERATE_MODEL_NAME = os.environ.get('DEFAULT_MODEL_NAME', '859-o3__2025-04-16')

# 函数定义
functions = [
    {
        "name": "open_and_save_page",
        "description": "使用 Playwright 打开指定的 URL，获取页面的 HTML 内容，并将其保存为指定文件。",
        "parameters": {
            "type": "object",
            "properties": {
                "url": {"type": "string", "description": "要打开的页面 URL"},
                "save_name": {"type": "string", "description": "保存 HTML 内容的文件名"},
                "headless": {"type": "boolean", "description": "是否以无界面模式启动浏览器，默认为false"}
            },
            "required": ["url", "save_name", "headless"]
        }
    },
    {
        "name": "open_and_save_page_with_remote_browser",
        "description": "使用远程浏览器打开指定的 URL，获取页面的 HTML 内容，并将其保存为指定文件。",
        "parameters": {
            "type": "object",
            "properties": {
                "url": {"type": "string", "description": "要打开的页面 URL"},
                "save_name": {"type": "string", "description": "保存 HTML 内容的文件名"},
            },
            "required": ["url", "save_name"]
        }
    },
    {
        "name": "clean_html",
        "description": "清理 HTML 内容，去除无关标签和注释，返回干净的 HTML。",
        "parameters": {
            "type": "object",
            "properties": {
                "html_path": {"type": "string", "description": "HTML 文件路径"},
                "save_name": {"type": "string", "description": "保存清洗后的 HTML 内容的文件名"}
            },
            "required": ["html_path", "save_name"]
        }
    },
    {
        "name": "get_playwright_code",
        "description": "调用大模型,根据给定的html文件(可能为多个)和提示词生成playwright_code代码,可能用于中途代码生成和最终代码生成"
                       "注意:每次在调用该函数时,前三个参数必须传递",
        "parameters": {
            "type": "object",
            "properties": {
                "clean_html_path_list": {"type": "string", "description": "需要清洗之后的html路径，多个路径用逗号分隔"},
                "model_response": {"type": "string", "description": "提示词,根据当前上下文生成,"},
                "code_file_path": {"type": "string", "description": "生成的python文件的名称,不包含目录名称"},
                "fail_messages": {"type": "string", "description": "错误日志,一般为none"}
            },
            "required": ["clean_html_path_list", "model_response", "code_file_path,fail_messages"]
        }
    },
    {
        "name": "execute_playwright_code",
        "description": "执行保存的 Playwright Python 代码。",
        "parameters": {
            "type": "object",
            "properties": {
                "code_file_path": {"type": "string", "description": "保存 Playwright 代码的文件路径"}
            },
            "required": ["code_file_path"]
        }
    },
    {
        "name": "edit_code",
        "description": "保存新的代码到本地。",
        "parameters": {
            "type": "object",
            "properties": {
                "new_code_path": {"type": "string", "description": "新的代码保存路径"},
                "new_python_code": {"type": "string", "description": "新的python代码"}
            },
            "required": ["new_code_path", "new_python_code"]
        }
    },
    # {
    #     "name": "list_directory_contents",
    #     "description": "检索指定目录下的所有文件和子目录。",
    #     "parameters": {
    #         "type": "object",
    #         "properties": {
    #             "directory_path": {"type": "string", "description": "要检索的目录路径"},
    #             "recursive": {"type": "boolean", "description": "是否递归检索子目录，默认为true"}
    #         },
    #         "required": ["directory_path"]
    #     }
    # },
    {
        "name": "read_file_content",
        "description": "读取指定文件的内容，支持多种编码格式。",
        "parameters": {
            "type": "object",
            "properties": {
                "file_path": {"type": "string", "description": "要读取的文件路径"},
                "encoding": {"type": "string", "description": "文件编码，默认为utf-8"}
            },
            "required": ["file_path"]
        }
    },
    {
        "name": "check_file_exists",
        "description": "检查指定路径的文件是否存在",
        "parameters": {
            "type": "object",
            "properties": {
                "file_path": {"type": "string", "description": "要检查的文件路径,必须是绝对路径,根据历史message来自己决定"}
            },
            "required": ["file_path"]
        }
    },
    {
        "name": "delete_files",
        "description": "删除指定路径的文件或目录，支持模糊匹配",
        "parameters": {
            "type": "object",
            "properties": {
                "path_pattern": {"type": "string", "description": "要删除的文件路径或模式，支持通配符*和?"},
                "directory": {"type": "string",
                              "description": "要在哪个目录下查找文件，可选值：html、code、data、all，默认为all"},
                "recursive": {"type": "boolean", "description": "是否递归删除子目录中的文件，默认为false"}
            },
            "required": ["path_pattern"]
        }
    }
]
import os
import json
import platform
import subprocess
from pathlib import Path
from typing import List, Optional, Dict
import logging

logger = logging.getLogger("app")

class BrowserConfigManager:
    """浏览器配置管理器"""
    
    def __init__(self, config_file: str = "browser_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"配置文件加载失败: {e}，将使用默认配置")
        
        return {"browser_executable_path": None}
    
    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.info(f"配置已保存到: {self.config_file}")
        except IOError as e:
            logger.error(f"配置文件保存失败: {e}")
    
    def _find_chrome_installations(self) -> List[str]:
        """查找本地Chrome浏览器安装路径"""
        chrome_paths = []
        system = platform.system()
        
        if system == "Windows":
            # Windows常见Chrome安装路径
            possible_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"),
                os.path.expandvars(r"%PROGRAMFILES%\Google\Chrome\Application\chrome.exe"),
                os.path.expandvars(r"%PROGRAMFILES(X86)%\Google\Chrome\Application\chrome.exe"),
                os.path.expandvars(r"%LOCALAPPDATA%\Chromium\Application\chrome.exe"),
                os.path.expandvars(r"%PROGRAMFILES%\Chromium\Application\chrome.exe"),
                os.path.expandvars(r"%PROGRAMFILES(X86)%\Chromium\Application\chrome.exe"),
                # Edge浏览器
                os.path.expandvars(r"%PROGRAMFILES%\Microsoft\Edge\Application\msedge.exe"),
                os.path.expandvars(r"%PROGRAMFILES(X86)%\Microsoft\Edge\Application\msedge.exe"),
            ]
        elif system == "Darwin":  # macOS
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
            ]
        else:  # Linux
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser",
                "/snap/bin/chromium",
                "/usr/bin/microsoft-edge",
            ]
        
        # 检查路径是否存在
        for path in possible_paths:
            if os.path.exists(path) and os.path.isfile(path):
                chrome_paths.append(path)
        
        # 尝试通过注册表查找（仅Windows）
        if system == "Windows":
            chrome_paths.extend(self._find_chrome_from_registry())
        
        # 去重并返回
        return list(set(chrome_paths))
    
    def _find_chrome_from_registry(self) -> List[str]:
        """通过Windows注册表查找Chrome路径"""
        chrome_paths = []
        try:
            import winreg
            
            # 查找Chrome安装路径的注册表位置
            registry_paths = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"),
            ]
            
            for hkey, subkey in registry_paths:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        path, _ = winreg.QueryValueEx(key, "")
                        if os.path.exists(path):
                            chrome_paths.append(path)
                except (WindowsError, FileNotFoundError):
                    continue
                    
        except ImportError:
            # winreg模块不可用（非Windows系统）
            pass
        except Exception as e:
            logger.warning(f"从注册表查找Chrome失败: {e}")
        
        return chrome_paths
    
    def get_browser_path(self) -> Optional[str]:
        """获取浏览器可执行文件路径"""
        # 首先检查配置文件中的路径
        configured_path = self.config.get("browser_executable_path")
        if configured_path and os.path.exists(configured_path):
            return configured_path
        
        # 如果配置文件中没有有效路径，则自动检测
        logger.info("配置文件中没有有效的浏览器路径，开始自动检测...")
        chrome_installations = self._find_chrome_installations()
        
        if not chrome_installations:
            logger.error("未找到任何Chrome浏览器安装")
            return None
        
        if len(chrome_installations) == 1:
            # 只找到一个，直接使用
            selected_path = chrome_installations[0]
            logger.info(f"自动选择浏览器: {selected_path}")
        else:
            # 找到多个，让用户选择
            selected_path = self._prompt_user_selection(chrome_installations)
        
        if selected_path:
            # 保存用户选择到配置文件
            self.config["browser_executable_path"] = selected_path
            self._save_config()
        
        return selected_path
    
    def _prompt_user_selection(self, chrome_paths: List[str]) -> Optional[str]:
        """提示用户选择浏览器"""
        print("\n发现多个浏览器安装，请选择要使用的浏览器：")
        print("-" * 60)
        
        for i, path in enumerate(chrome_paths, 1):
            browser_name = self._get_browser_name(path)
            print(f"{i}. {browser_name}")
            print(f"   路径: {path}")
            print()
        
        while True:
            try:
                choice = input(f"请输入选择 (1-{len(chrome_paths)}): ").strip()
                if choice.isdigit():
                    index = int(choice) - 1
                    if 0 <= index < len(chrome_paths):
                        selected_path = chrome_paths[index]
                        browser_name = self._get_browser_name(selected_path)
                        print(f"已选择: {browser_name}")
                        return selected_path
                
                print("无效选择，请重新输入")
            except (KeyboardInterrupt, EOFError):
                print("\n用户取消选择")
                return None
    
    def _get_browser_name(self, path: str) -> str:
        """根据路径获取浏览器名称"""
        path_lower = path.lower()
        if "chrome" in path_lower:
            if "chromium" in path_lower:
                return "Chromium"
            else:
                return "Google Chrome"
        elif "edge" in path_lower or "msedge" in path_lower:
            return "Microsoft Edge"
        else:
            return os.path.basename(path)
    
    def set_browser_path(self, path: str) -> bool:
        """手动设置浏览器路径"""
        if not os.path.exists(path):
            logger.error(f"浏览器路径不存在: {path}")
            return False
        
        self.config["browser_executable_path"] = path
        self._save_config()
        logger.info(f"浏览器路径已设置为: {path}")
        return True
    
    def reset_config(self):
        """重置配置"""
        self.config = {"browser_executable_path": None}
        self._save_config()
        logger.info("浏览器配置已重置")


# 全局配置管理器实例（使用默认配置文件路径）
browser_config_manager = BrowserConfigManager()

def get_browser_executable_path() -> Optional[str]:
    """获取浏览器可执行文件路径的便捷函数（使用默认配置管理器）"""
    return browser_config_manager.get_browser_path()

# 导出 BrowserConfigManager 类，以便其他模块可以创建自定义配置管理器实例
__all__ = ['get_browser_executable_path', 'browser_config_manager', 'BrowserConfigManager']

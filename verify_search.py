from lxml import html

with open('temp/html/baidu_home_raw_clean_fixed.html', 'r', encoding='utf-8') as f:
    content = f.read()

tree = html.fromstring(content)

# 查找搜索表单
form = tree.xpath('//form[@id="form"]')[0]
print('搜索表单HTML:')
form_html = html.tostring(form, encoding='unicode', pretty_print=True)
print(form_html[:800])

print('\n' + '='*50)
print('搜索输入框:')
search_input = tree.xpath('//input[@id="kw"]')[0]
input_html = html.tostring(search_input, encoding='unicode')
print(input_html)

print('\n' + '='*50)
print('搜索按钮:')
search_button = tree.xpath('//input[@id="su"]')[0]
button_html = html.tostring(search_button, encoding='unicode')
print(button_html)

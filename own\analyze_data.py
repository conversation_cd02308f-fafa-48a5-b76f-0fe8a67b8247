import json
import os

# 找到最新的数据文件
data_files = [f for f in os.listdir('.') if f.startswith('priceless_data_') and f.endswith('.json')]
if not data_files:
    print("没有找到数据文件")
    exit()

latest_file = max(data_files)
print(f"分析文件: {latest_file}")

# 读取数据
with open(latest_file, 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"总共抓取了 {len(data)} 条数据")

if data:
    print("\n第一条数据的键:")
    print(list(data[0].keys()))
    
    print("\n前5条数据的信息:")
    for i, item in enumerate(data[:5]):
        print(f"{i+1}. 标题: {item.get('title', 'N/A')}")
        print(f"   价格: {item.get('price', 'N/A')}")
        print(f"   位置: {item.get('location', 'N/A')}")
        print(f"   详情URL: {item.get('detail_url', 'N/A')}")
        print(f"   图片URL: {item.get('image_url', 'N/A')}")
        print(f"   描述长度: {len(item.get('description', ''))}")
        print() 
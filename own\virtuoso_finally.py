#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse, asyncio, base64, json, logging, math, os, re, socket, time
from pathlib import Path
from urllib.parse import urljoin

import requests
import lxml.html as lh
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# ---------- 常量与配置 ----------
BASE = "https://www.virtuoso.com"
API = f"{BASE}/hotels/GetSearchView"
BATCH = int(time.time() * 1000)
HOST = socket.gethostname()
ROWS_PER_PAGE = 25

OCT_URL = "https://htloct-core-gateway.trip.com/core-server/result"
AUTH_HDR = "Basic " + base64.b64encode(b"wenbochen:123456").decode()

HEADERS_JSON = {
    "Origin": BASE,
    "Referer": f"{BASE}/hotels",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36",
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest",
}
HEADERS_HTML = {"User-Agent": HEADERS_JSON["User-Agent"]}
cookies = {
    "_fbp": "fb.1.1750412650775.406833061790285290",
    "csrf_token": "be4e16a5-506b-4ce4-a975-bc3b9d18cba9",
    "hubspotutk": "795d4950af71059a7a4781f536834830",
    "CookiesEnabled": "true",
    "slc": "v4Xa9eo4G5xqcc0D9OvFZzE7yYvqagyh4xFHens+jErYBoin7VBFw18m080dKmHOvI7KDn3YrlHbwM1m25oqQKulGfo760s5o3rnRM/AIQnbPORt3126SW5c31XarZwD",
    "CMSPreferredCulture": "en-US",
    "ApplicationGatewayAffinityProCORS": "4529d072b24741a0b8713d1c5d97d0c8",
    "ApplicationGatewayAffinityPro": "4529d072b24741a0b8713d1c5d97d0c8",
    "ASP.NET_SessionId": "xahvplckbgkdqepjeiy1f4xd",
    "ATC": "LastLoggedInAs=Anonymous&HasLoggedInBefore=0&SessionId=xahvplckbgkdqepjeiy1f4xd",
    "__hssrc": "1",
    "CMSCsrfCookie": "BJuuKUCnzVsEPVRA5SiDCultR/ClMRNUwzrmzj53",
    ".AspNetCore.Session": "CfDJ8KboiWSMgc9FnF9Uq4q%2BuXzR42h583eKhnI9OnfcZD05ieCTR2TZhJqV0%2BvQUJHT2jHgs48uZ45%2BHqjDT8PFIW69o1AEjLfVD6rHqbr7CwNn4BRhTha9o0n2fgpsocmJLMezVq5c%2BOJvUJCn4JeCOPCCMfZNChq8xLOjUznjbf1N",
    "_gcl_au": "1.1.1304411834.1751010462",
    "_ga": "GA1.1.1431330017.1751010462",
    "__hstc": "78695051.795d4950af71059a7a4781f536834830.1750412651251.1750997349355.1751010482337.15",
    "timerStarted": "1751011260495",
    "_ga_8DXKLVYT9K": "GS2.1.s1751010462$o1$g1$t1751011260$j60$l0$h1568088759",
    "__hssc": "78695051.5.1751010482337"
}

# ---------- 文件路径 ----------
CKPT_FILE = Path("checkpoint.json")
FAILED_FILE = Path("failed_pages.txt")
DONE_FILE = Path("done_ids.txt")
FAILED_DETAIL_FILE = Path("still_failed_retry.txt")
FINAL_FAILED_FILE = Path("final_failed.txt")

# ---------- 日志配置 ----------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("virtuoso")


# ---------- Octopus Upload ----------
def build_octopus_payload(rows, ds_type, lvl="P5"):
    return {
        "taskType": 22222222,
        "buildTime": BATCH,
        "taskId": -1,
        "taskLevel": lvl,
        "resultDetail": {
            "classType": "com.ctrip.fx.octopus.model.DefaultDBPersistenceResultDetail",
            "dsType": ds_type,
            "replace": True,
            "result": rows,
        },
    }

@retry(
    retry=retry_if_exception_type(Exception),
    wait=wait_exponential(multiplier=1, min=2, max=20),
    stop=stop_after_attempt(3),
    reraise=True
)
def octopus_upload(rows, ds_type):
    if not rows:
        return
    resp = requests.post(
        OCT_URL,
        headers={"Authorization": AUTH_HDR, "Content-Type": "application/json;charset=UTF-8"},
        data=json.dumps(build_octopus_payload(rows, ds_type), ensure_ascii=False).encode(),
        timeout=30
    )
    if not (resp.ok and resp.json().get("code") == 0):
        raise RuntimeError(f"octopus error {resp.status_code} {resp.text}")

# ---------- 工具 ----------
def make_payload(page):
    opts = {
        "CurrentPage": str(page),
        "RowsPerPage": str(ROWS_PER_PAGE),
        "SearchType": "Property",
        "SortType": "HotelNameAsc",
        "ProductIds": [],
        "SelectedFacets": [],
    }
    return json.dumps({"options": {"ClientSideOptions": opts}}, separators=(",", ":"))

def load_checkpoint():
    return json.loads(CKPT_FILE.read_text(encoding="utf-8")).get("next_page", 1) if CKPT_FILE.exists() else 1

def save_checkpoint(next_page: int):
    CKPT_FILE.write_text(json.dumps({"next_page": next_page}), encoding="utf-8")

def norm_list_row(h):
    return {
        "telephone": h.get("Phone", "").strip(),
        "email": h.get("Email", "").strip(),
        "href": urljoin(BASE, h.get("DetailUrl", "")),
        "hotelId": h.get("Id", ""),
        "hotelName": h.get("Name", "").strip(),
        "address": h.get("CityStateCountry", ""),
        "clientCode": HOST,
        "buildTime": BATCH,
        "userdata_location": "",
    }

def parse_detail_html(url, html):
    tree = lh.fromstring(html)
    hotel_id = re.search(r"/hotels/(\d+)/", url).group(1)
    hotel_name = tree.xpath("string(//h1[@id='hotelTitle'])") or tree.xpath("string(//title)")
    phone = tree.xpath("string(//input[@id='HotelPrimaryPhone']/@value)").strip()
    email = tree.xpath("string(//input[@id='HotelPrimaryEmail']/@value)").strip()
    lat = lon = addr_json = hotel_desc = ""

    for raw in tree.xpath("//script[@type='application/ld+json']/text()"):
        try:
            data = json.loads(raw)
            for item in (data if isinstance(data, list) else [data]):
                if item.get("@type") in {"Hotel", "LodgingBusiness"}:
                    lat = item.get("latitude", lat)
                    lon = item.get("longitude", lon)
                    if item.get("address"):
                        addr_json = json.dumps(item["address"], ensure_ascii=False)
                    hotel_desc = item.get("description", hotel_desc)
        except:
            continue

    return {
        "hotelId": hotel_id, "hotelName": hotel_name.strip(),
        "telephone": phone, "email": email,
        "lat": lat, "lng": lon,
        "address": addr_json,
        "hotelDesc": hotel_desc.strip(),
        "href": url,
        "clientCode": HOST,
        "buildTime": BATCH,
    }

# ---------- 列表页主逻辑 ----------
def crawl_list_and_details(max_pages=None):
    page = load_checkpoint()
    detail_urls = []

    while True:
        try:
            r = requests.post(API, headers=HEADERS_JSON, data=make_payload(page), timeout=20)
            r.raise_for_status()
            j = r.json()
        except Exception as e:
            logger.error(f"列表页失败: 第{page}页 – {e}")
            save_checkpoint(page + 1)
            page += 1
            continue

        hotels = j.get("Hotels", [])
        if not hotels:
            break

        list_rows = [norm_list_row(h) for h in hotels]
        octopus_upload(list_rows, "htlVendorPriceMDB.virtuoso_hotel_list")
        detail_urls.extend(r["href"] for r in list_rows)

        page += 1
        save_checkpoint(page)
        time.sleep(0.2)
        if max_pages and page > max_pages:
            break

    return detail_urls

# ---------- 异步抓详情 ----------
async def run_detail_workers(urls, concurrency=10):
    sem = asyncio.Semaphore(concurrency)
    buf = []

    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as sess:
        async def worker(url):
            nonlocal buf
            async with sem:
                try:
                    async with sess.get(url, headers=HEADERS_HTML, timeout=30) as resp:
                        resp.raise_for_status()
                        text = await resp.text()
                        detail = parse_detail_html(url, text)
                        buf.append(detail)
                        if len(buf) >= 50:
                            octopus_upload(buf, "htlVendorPriceMDB.virtuoso_hotel_info")
                            buf.clear()
                except Exception as e:
                    logger.warning(f"❌ 详情失败 {url} – {e}")

        await asyncio.gather(*(worker(u) for u in urls))

    if buf:
        octopus_upload(buf, "htlVendorPriceMDB.virtuoso_hotel_info")

# ---------- 重试失败详情页 ----------
def retry_failed_details():
    if not FAILED_DETAIL_FILE.exists():
        return
    urls = [u.strip() for u in FAILED_DETAIL_FILE.read_text(encoding="utf-8").splitlines() if u.strip()]
    if not urls:
        return

    failed = []
    for url in urls:
        try:
            print(f"🔁 重试：{url}")
            r = requests.get(url, headers=HEADERS_HTML, cookies=COOKIES, timeout=30)
            r.raise_for_status()
            detail = parse_detail_html(url, r.text)
            octopus_upload([detail], "htlVendorPriceMDB.virtuoso_hotel_info")
            print(f"✅ 成功上传：{url}")
        except Exception as e:
            print(f"❌ 再次失败：{url} | 错误: {e}")
            failed.append(url)

    if failed:
        FINAL_FAILED_FILE.write_text("\n".join(failed), encoding="utf-8")
        print(f"\n⚠️ 写入最终失败文件：{FINAL_FAILED_FILE.name}")

# ---------- 主入口 ----------
if __name__ == "__main__":
    pa = argparse.ArgumentParser()
    pa.add_argument("-m", "--max-pages", type=int, default=None)
    pa.add_argument("-c", "--concurrency", type=int, default=10)
    args = pa.parse_args()

    all_detail_urls = crawl_list_and_details(args.max_pages)
    asyncio.run(run_detail_workers(all_detail_urls, args.concurrency))
    retry_failed_details()

/**
 * UI工具函数
 * 处理界面元素的交互和展示
 */

const MAIN_STEPS = [
    '正在优化提示词', '提示词优化完成', '正在生成代码', '代码生成完成',
    '正在执行代码', '代码执行完成', '任务已完成', '正在处理', '正在分析', '任务完成', '执行Playwright代码', '已完成', '已取消', '已拒绝', '已终止'
];

/**
 * 添加日志到进度面板
 * @param {string} message - 日志消息
 * @param {string} type - 日志类型（success, error, 或空）
 */
function addLog(message, type = '') {
    // 只显示主流程
    if (!MAIN_STEPS.some(step => message.includes(step))) return;

    const progressSteps = document.getElementById('progressSteps');

    // 判断是否为进行中还是完成
    let isProcessing = /正在|处理中|分析|执行|生成|优化/.test(message) && !/完成|已完成|已取消|已拒绝|已终止/.test(message);
    let isDone = /完成|已完成|任务完成|已取消|已拒绝|已终止/.test(message);

    // 步骤高亮与勾
    const li = document.createElement('li');
    li.className = 'progress-step';
    if (isProcessing) {
        li.innerHTML = `<span class="text-primary fw-bold">${message}</span>`;
    } else if (isDone) {
        li.innerHTML = `<span class="text-success"><i class="bi bi-check-circle-fill me-1"></i>${message}</span>`;
    } else {
        li.textContent = message;
    }
    progressSteps.appendChild(li);
    progressSteps.scrollTop = progressSteps.scrollHeight;

    // 添加到历史
    addToHistory('assistant', message);
}

/**
 * 添加消息到历史面板
 * @param {string} role - 消息角色（user, assistant, function）
 * @param {string} content - 消息内容
 * @param {string} type - 消息类型（通常用于错误标记等）
 */
function addToHistory(role, content, type = '') {
    const messageHistory = document.getElementById('messageHistory');
    if (!messageHistory) return;
    const historyItem = document.createElement('li');
    historyItem.className = `history-item ${role} ${type}`;

    const roleBadge = document.createElement('span');
    roleBadge.className = 'badge bg-secondary me-2';

    if (role === 'user') {
        roleBadge.textContent = '用户';
        roleBadge.className = 'badge bg-primary me-2';
    } else if (role === 'assistant') {
        roleBadge.textContent = '助手';
        roleBadge.className = 'badge bg-success me-2';
    } else if (role === 'function') {
        roleBadge.textContent = '函数';
        roleBadge.className = 'badge bg-warning text-dark me-2';
    }

    historyItem.appendChild(roleBadge);

    const contentSpan = document.createElement('span');
    contentSpan.textContent = content;
    historyItem.appendChild(contentSpan);

    messageHistory.appendChild(historyItem);
}

/**
 * 格式化优化后的提示词
 * @param {string} prompt - 原始提示词文本
 * @returns {string} 格式化后的HTML内容
 */
function formatPrompt(prompt) {
    if (!prompt) return '';

    // 添加拷贝按钮（使用转义后的prompt）
    const escapedForCopy = prompt.replace(/'/g, "\\'").replace(/\n/g, "\\n");
    let formattedHtml = '<div class="d-flex justify-content-end mb-2">' +
        '<button class="btn btn-sm btn-outline-secondary copy-prompt-btn" ' +
        'onclick="navigator.clipboard.writeText(\'' + escapedForCopy + '\');">' +
        '<i class="bi bi-clipboard me-1"></i>复制全文</button></div>';

    // 预处理：修复明显的格式错误
    // 修复格式错误的URL显示，如 "使用本地浏览器访问指定页面HTML源码。" target="_blank" class="prompt-url">https://example.com，
    prompt = prompt.replace(/(使用(?:本地|远程)浏览器访问[^"<>]+)"\s+target="_blank"\s+class="prompt-url">([^<，,]+)[，,]/g,
        '$1：$2，');

    // 创建新的文本处理函数，避免正则表达式的嵌套问题
    function processText(text) {
        // 基础转义，防止XSS攻击
        let processed = text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");

        // 替换换行符为HTML换行
        processed = processed.replace(/\n/g, '<br>');

        return processed;
    }

    // 第一步：先完全转义所有文本
    let formatted = processText(prompt);

    // 第二步：分段处理
    // 检测是否有明确的步骤格式
    let hasSteps = /(\d+\.\s+[^\n<]+)/.test(formatted);

    if (hasSteps) {
        // 处理编号步骤
        formatted = formatted.replace(/(\d+\.\s+[^<]+)(<br>|$)/g,
            '<div class="prompt-step">$1</div>$2');
    } else {
        // 尝试通过空行分段
        formatted = formatted.replace(/(<br>){2,}/g, '</p><p>');
        formatted = '<p>' + formatted + '</p>';
    }

    // 第三步：高亮特定内容（按优先级顺序处理）

    // 1. 处理URL链接
    formatted = formatted.replace(/https?:\/\/[^\s<>"']+/g, function(match) {
        // 确保URL是安全的
        const safeUrl = match
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
        return `<a href="${safeUrl}" target="_blank" class="prompt-url">${safeUrl}</a>`;
    });

    // 2. 处理函数调用
    const functions = [
        'open_and_save_page_with_remote_browser',
        'open_and_save_page',
        'clean_html',
        'get_playwright_code',
        'execute_playwright_code',
        'edit_code',
        'list_directory_contents',
        'read_file_content',
        'check_file_exists',
        'delete_files'
    ];

    const functionPattern = new RegExp('\\b(' + functions.join('|') + ')\\b', 'g');
    formatted = formatted.replace(functionPattern, '<span class="prompt-function">$1</span>');

    // 3. 处理小标题或强调部分
    const titlePatterns = [
        '任务目标', '执行步骤', '具体要求', '步骤', '优化后的提示词',
        '重要', '注意', '提示', '说明', '注意事项', '数据要求',
        '翻页要求', '数据格式', '特殊处理'
    ];

    const titlePattern = new RegExp('(' + titlePatterns.join('|') + ')([：:])\\s*', 'g');
    formatted = formatted.replace(titlePattern, '<div class="prompt-title">$1$2</div>');

    // 4. 处理强调部分（方括号和中括号）
    formatted = formatted.replace(/[【\[](.*?)[】\]]/g, '<span class="prompt-emphasis">【$1】</span>');

    // 5. 处理关键词高亮
    const keywords = ['列表页', '详情页', '翻页', '提取', '采集', '保存',
        'HTML', 'CSV', 'JSON', 'Excel', '数据', '文件'];

    const keywordPattern = new RegExp('\\b(' + keywords.join('|') + ')\\b', 'g');
    formatted = formatted.replace(keywordPattern, '<span class="prompt-keyword">$1</span>');

    return formattedHtml + formatted;
}

/**
 * 更新表单生成的任务描述
 * @returns {string} 生成的任务描述
 */
function updatePromptSummary() {
    const taskUrl = document.getElementById('taskUrl');
    const taskType = document.getElementById('taskType');
    const dataRequirements = document.getElementById('dataRequirements');
    const dataFormat = document.getElementById('dataFormat');
    const needPagination = document.getElementById('needPagination');
    const pageCount = document.getElementById('pageCount');
    const additionalRequirements = document.getElementById('additionalRequirements');

    const url = taskUrl.value.trim();
    const type = taskType.options[taskType.selectedIndex].text;
    const requirements = dataRequirements.value.trim();
    const format = dataFormat.options[dataFormat.selectedIndex].text;
    const pagination = needPagination.checked;
    const pages = pagination ? pageCount.options[pageCount.selectedIndex].text : '不需要翻页';
    const additional = additionalRequirements.value.trim();

    let summary = '';

    if (url) {
        summary += `访问${url}，`;
    }

    if (type.includes('列表详情')) {
        summary += '拿到该页面的所有列表项信息，并获取每个列表项的详情页信息，';
    } else if (type.includes('搜索')) {
        summary += '进行搜索并获取搜索结果，';
    } else if (type.includes('单页')) {
        summary += '从本页面获得信息，';
    } else {
        // 自定义类型
        if (requirements) {
            summary += `执行数据抓取任务，`;
        }
    }

    if (requirements) {
        summary += `需要采集的数据包括：${requirements}，`;
    }

    // 添加数据格式及其配置信息
    if (format) {
        let formatDetails = '';
        switch (dataFormat.value) {
            case 'csv':
                const csvColumns = document.getElementById('csvColumns');
                const csvIncludeHeader = document.getElementById('csvIncludeHeader');
                const csvEncoding = document.getElementById('csvEncoding');
                
                if (csvColumns.value) {
                    formatDetails += `，列名为：${csvColumns.value}`;
                }
                if (!csvIncludeHeader.checked) {
                    formatDetails += '，不包含表头';
                }
                if (csvEncoding.value !== 'utf-8') {
                    formatDetails += `，使用${csvEncoding.options[csvEncoding.selectedIndex].text}编码`;
                }
                break;
            case 'json':
                const jsonStructure = document.getElementById('jsonStructure');
                const jsonFields = document.getElementById('jsonFields');
                const jsonPretty = document.getElementById('jsonPretty');
                
                formatDetails += `，结构为${jsonStructure.options[jsonStructure.selectedIndex].text}`;
                if (jsonFields.value) {
                    formatDetails += `，字段名为：${jsonFields.value}`;
                }
                if (!jsonPretty.checked) {
                    formatDetails += '，不进行格式美化';
                }
                break;
            case 'excel':
                const excelColumns = document.getElementById('excelColumns');
                const excelSheetName = document.getElementById('excelSheetName');
                const excelAutoFilter = document.getElementById('excelAutoFilter');
                const excelFreeze = document.getElementById('excelFreeze');
                
                if (excelColumns.value) {
                    formatDetails += `，列名为：${excelColumns.value}`;
                }
                if (excelSheetName.value && excelSheetName.value !== '数据') {
                    formatDetails += `，工作表名称为：${excelSheetName.value}`;
                }
                let excelFeatures = [];
                if (excelAutoFilter.checked) excelFeatures.push('添加筛选');
                if (excelFreeze.checked) excelFeatures.push('冻结首行');
                if (excelFeatures.length > 0) {
                    formatDetails += `，${excelFeatures.join('和')}`;
                }
                break;
            case 'txt':
                const txtSeparator = document.getElementById('txtSeparator');
                const customSeparator = document.getElementById('customSeparator');
                const txtEncoding = document.getElementById('txtEncoding');
                
                let separator = txtSeparator.value;
                if (separator === 'custom' && customSeparator.value) {
                    separator = customSeparator.value;
                } else if (separator === 'custom') {
                    separator = '自定义分隔符';
                } else {
                    separator = txtSeparator.options[txtSeparator.selectedIndex].text;
                }
                formatDetails += `，使用${separator}作为分隔符`;
                if (txtEncoding.value !== 'utf-8') {
                    formatDetails += `，使用${txtEncoding.options[txtEncoding.selectedIndex].text}编码`;
                }
                break;
        }
        summary += `将数据保存为${format}格式${formatDetails}，`;
    }

    if (pagination) {
        if (pages === '所有页') {
            summary += '需要获取所有页面的数据，';
        } else {
            summary += `最多翻${pages}，`;
        }
    }

    if (additional) {
        summary += `其他要求：${additional}，`;
    }

    // 移除最后的逗号和空格
    summary = summary.replace(/，\s*$/, '。');

    return summary;
}

/**
 * 更新格式选项的显示/隐藏
 */
function updateFormatOptions() {
    const dataFormat = document.getElementById('dataFormat');
    const format = dataFormat.value;
    
    const csvOptions = document.getElementById('csvOptions');
    const jsonOptions = document.getElementById('jsonOptions');
    const excelOptions = document.getElementById('excelOptions');
    const txtOptions = document.getElementById('txtOptions');

    // 隐藏所有格式选项
    csvOptions.classList.add('hidden');
    jsonOptions.classList.add('hidden');
    excelOptions.classList.add('hidden');
    txtOptions.classList.add('hidden');

    // 显示选中的格式选项
    switch (format) {
        case 'csv':
            csvOptions.classList.remove('hidden');
            break;
        case 'json':
            jsonOptions.classList.remove('hidden');
            break;
        case 'excel':
            excelOptions.classList.remove('hidden');
            break;
        case 'txt':
            txtOptions.classList.remove('hidden');
            break;
    }
} 
from playwright.sync_api import sync_playwright
from config import BROWSER_EXECUTABLE_PATH

# 隐藏playwright特征
with sync_playwright() as pw:
    chrome_path = BROWSER_EXECUTABLE_PATH

    # 1. 启动 Chromium 浏览器，添加参数禁用明显的自动化特征
    browser = pw.chromium.launch(
        headless=False,  # 使用有头模式更接近真实浏览器
        executable_path=chrome_path
    )

    # 5. 新建页面
    page = browser.new_page()

    # 模拟打开目标网站（此处以示例站点代替），进入页面
    page.goto("https://www.example.com/")

    # 关闭浏览器
    browser.close()

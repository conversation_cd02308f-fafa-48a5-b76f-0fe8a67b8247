import requests
import re
import time
import json
from urllib.parse import urlencode

class PricelessScraper:
    def __init__(self):
        # 使用用户提供的最新cookie
        self.cookie_string = "OptanonAlertBoxClosed=2025-06-04T04:14:18.548Z; AMCV_919F3704532951060A490D44%40AdobeOrg=179643557%7CMCMID%7C37995462196494767108015999227060995520%7CvVersion%7C5.5.0; _fbp=fb.1.1749010461117.960934812518646035; stk=Sk03OHdwZlZpR0hhWXFjMUU4aGpsTkR1N35LZ2N2UzT7wwLaf0M_TNPvKW2FF9wwOj2mpgQtdpBDyS0_ahi9ow%3D%3D; S=QjB2RzsSFfy0s5OeQbm8; OptanonConsent=isGpcEnabled=0&datestamp=Wed+Jul+02+2025+17%3A24%3A35+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202505.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=d37fdb49-1ca9-458c-85d6-99d4d383fd64&interactionCount=2&isAnonUser=1&landingPath=NotLandingPage&groups=C015%3A1%2CC048%3A1%2CC076%3A1%2CC0001%3A1%2CC006%3A1%2CC0002%3A1%2CC054%3A1%2CC0003%3A1%2CC011%3A1%2CC020%3A1%2CC0004%3A1&AwaitingReconsent=false&intType=1&geolocation=CN%3BSH; gpv_pn=search.filterpage; s_ips=314; s_tp=1723; s_ppv=search.filterpage%2C18%2C18%2C314%2C1%2C5; s_cc=true; s_plt=8.60; s_pltp=search.filterpage; s_nr365=1751448323491-Repeat; s_sq=masterc611%252Cmastercglobal%3D%2526c.%2526a.%2526activitymap.%2526page%253Dsearch.filterpage%2526link%253DEnglish%2526region%253Dlanguage-scroll%2526pageIDType%253D1%2526.activitymap%2526.a%2526.c%2526pid%253Dsearch.filterpage%2526pidt%253D1%2526oid%253DEnglish%2526oidt%253D3%2526ot%253DSUBMIT; C=46-1-92-1-0; P=%2B52gOhPF%2BTyI%2F2BY5wU%2Fv%2BaxVnQuJOUuUUgoyP5a0Hzhh%2Blmu%2FtnGoCdLRN9X6zSOM%2FUo4eCq%2FfZrA2BryZ0qmF1dO1XY%2FQ7i87%2Bckfd9FjQz%2B879ZFmJApgN2%2BPqo6e%2Fen-US%7C___%7C0"
        
        # 解析cookie字符串为字典
        self.cookies = {}
        for item in self.cookie_string.split('; '):
            if '=' in item:
                key, value = item.split('=', 1)
                self.cookies[key] = value
        
        self.session = requests.Session()
        self.session.cookies.update(self.cookies)
        
        self.base_url = "https://www.priceless.com/services/search/getNextBlocksProducts"
        self.main_page_url = "https://www.priceless.com/filter/options/contentType/1"
        
    def get_total_count_from_page(self):
        """从主页面获取总数目"""
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "sec-ch-ua": "\"Google Chrome\";v=\"117\", \"Not;A=Brand\";v=\"8\", \"Chromium\";v=\"117\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\""
        }
        
        try:
            print("正在请求主页面获取总数目...")
            response = self.session.get(self.main_page_url, headers=headers)
            print(f"主页面响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print("主页面请求成功！")
                
                # 尝试多种模式匹配总数目
                patterns = [
                    r'# (\d+) Results',
                    r'(\d+)\s+Results',
                    r'"totalTileCnt":\s*"(\d+)"',
                    r'"totalTileCnt":\s*(\d+)',
                    r'totalTileCnt["\']?\s*[:=]\s*["\']?(\d+)',
                    r'g_block_pCnt\s*=\s*["\'](\d+)["\']',
                    r'loadedTitleCnt["\']?\s*[:=]\s*["\']?(\d+)',
                ]
                
                total_count = None
                for i, pattern in enumerate(patterns):
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        print(f"模式 {i+1} 找到匹配: {matches}")
                        numbers = [int(m) for m in matches]
                        potential_total = max(numbers)
                        if potential_total > 100:
                            total_count = potential_total
                            print(f"使用模式 {i+1} 找到总数目: {total_count}")
                            break
                
                if total_count is None:
                    # 查找所有数字
                    all_numbers = re.findall(r'\b(\d+)\b', content)
                    number_counts = {}
                    for num in all_numbers:
                        if int(num) > 500 and int(num) < 10000:  # 合理的总数范围
                            number_counts[num] = number_counts.get(num, 0) + 1
                    
                    if number_counts:
                        print("找到的可能总数目:")
                        for num, count in sorted(number_counts.items(), key=lambda x: int(x[0]), reverse=True):
                            print(f"  数字 {num} 出现 {count} 次")
                        
                        total_count = int(max(number_counts.keys(), key=int))
                        print(f"选择最大数字作为总数目: {total_count}")
                
                return total_count or 4500  # 默认使用之前获取到的4500
                
            else:
                print(f"主页面请求失败，状态码: {response.status_code}")
                return 4500  # 使用默认值
                
        except Exception as e:
            print(f"请求主页面时出错: {e}")
            return 4500  # 使用默认值
    
    def get_page_data(self, loaded_count):
        """获取指定页面的数据"""
headers = {
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7",
    "Connection": "keep-alive",
    "Referer": "https://www.priceless.com/filter/options/contentType/1",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "X-Requested-With": "XMLHttpRequest",
    "sec-ch-ua": "\"Google Chrome\";v=\"117\", \"Not;A=Brand\";v=\"8\", \"Chromium\";v=\"117\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
        
        try:
            # 生成当前时间戳
            timestamp = str(int(time.time() * 1000))
            
params = {
    "catString": "",
    "geoString": "",
    "keyword": "",
    "fromDate": "",
    "shpString": "",
    "display": "",
    "contentString": "1",
    "programString": "",
    "cardString": "",
    "toDate": "",
    "weekDays": "",
    "weekNights": "",
    "weekends": "",
    "sortBy": "",
    "partnerCategoryFilter": "true",
    "date": "",
                "loadedTitleCnt": str(loaded_count),
    "totalTileCnt": "",
                "baseUrl": self.main_page_url,
                "_": timestamp
            }
            
            print(f"正在请求第 {loaded_count//24 + 1} 页数据 (已加载: {loaded_count})...")
            response = self.session.get(self.base_url, headers=headers, params=params)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            print(f"获取页面数据时出错: {e}")
            return None
    
    def scrape_all_data(self):
        """抓取所有数据"""
        print("开始抓取 Priceless 数据...")
        
        # 获取总数目
        total_count = self.get_total_count_from_page()
        print(f"总共需要抓取 {total_count} 条数据")
        
        # 计算需要请求的页数 (每页24条数据)
        items_per_page = 24
        total_pages = (total_count + items_per_page - 1) // items_per_page
        print(f"需要请求 {total_pages} 页数据")
        
        all_data = []
        loaded_count = 0
        
        for page in range(total_pages):
            try:
                # 获取当前页数据
                page_data = self.get_page_data(loaded_count)
                
                if page_data:
                    # 提取产品数据
                    if 'products' in page_data:
                        products = page_data['products']
                        all_data.extend(products)
                        loaded_count += len(products)
                        print(f"第 {page + 1} 页获取到 {len(products)} 条数据，累计: {loaded_count}")
                        
                        # 如果这页数据少于24条，说明已经是最后一页
                        if len(products) < items_per_page:
                            print("已获取完所有数据")
                            break
                    else:
                        print(f"第 {page + 1} 页没有找到产品数据")
                        print("响应内容:", page_data)
                        
                        # 如果连续几页都没有数据，可能已经结束
                        if page > 0:
                            break
                else:
                    print(f"第 {page + 1} 页请求失败")
                    
                # 添加延时避免请求过快
                time.sleep(1)
                
            except Exception as e:
                print(f"处理第 {page + 1} 页时出错: {e}")
                continue
        
        print(f"抓取完成！总共获取到 {len(all_data)} 条数据")
        return all_data
    
    def save_data(self, data, filename="priceless_data.json"):
        """保存数据到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存数据时出错: {e}")

def get_total_count_from_page():
    """从主页面获取总数目"""
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7",
        "Connection": "keep-alive",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "sec-ch-ua": "\"Google Chrome\";v=\"117\", \"Not;A=Brand\";v=\"8\", \"Chromium\";v=\"117\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\""
    }
    
    # 使用用户提供的最新cookie
    cookie_string = "OptanonAlertBoxClosed=2025-06-04T04:14:18.548Z; AMCV_919F3704532951060A490D44%40AdobeOrg=179643557%7CMCMID%7C37995462196494767108015999227060995520%7CvVersion%7C5.5.0; _fbp=fb.1.1749010461117.960934812518646035; stk=Sk03OHdwZlZpR0hhWXFjMUU4aGpsTkR1N35LZ2N2UzT7wwLaf0M_TNPvKW2FF9wwOj2mpgQtdpBDyS0_ahi9ow%3D%3D; S=QjB2RzsSFfy0s5OeQbm8; OptanonConsent=isGpcEnabled=0&datestamp=Wed+Jul+02+2025+17%3A24%3A35+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202505.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=d37fdb49-1ca9-458c-85d6-99d4d383fd64&interactionCount=2&isAnonUser=1&landingPath=NotLandingPage&groups=C015%3A1%2CC048%3A1%2CC076%3A1%2CC0001%3A1%2CC006%3A1%2CC0002%3A1%2CC054%3A1%2CC0003%3A1%2CC011%3A1%2CC020%3A1%2CC0004%3A1&AwaitingReconsent=false&intType=1&geolocation=CN%3BSH; gpv_pn=search.filterpage; s_ips=314; s_tp=1723; s_ppv=search.filterpage%2C18%2C18%2C314%2C1%2C5; s_cc=true; s_plt=8.60; s_pltp=search.filterpage; s_nr365=1751448323491-Repeat; s_sq=masterc611%252Cmastercglobal%3D%2526c.%2526a.%2526activitymap.%2526page%253Dsearch.filterpage%2526link%253DEnglish%2526region%253Dlanguage-scroll%2526pageIDType%253D1%2526.activitymap%2526.a%2526.c%2526pid%253Dsearch.filterpage%2526pidt%253D1%2526oid%253DEnglish%2526oidt%253D3%2526ot%253DSUBMIT; C=46-1-92-1-0; P=%2B52gOhPF%2BTyI%2F2BY5wU%2Fv%2BaxVnQuJOUuUUgoyP5a0Hzhh%2Blmu%2FtnGoCdLRN9X6zSOM%2FUo4eCq%2FfZrA2BryZ0qmF1dO1XY%2FQ7i87%2Bckfd9FjQz%2B879ZFmJApgN2%2BPqo6e%2Fen-US%7C___%7C0"
    
    # 解析cookie字符串为字典
    cookies = {}
    for item in cookie_string.split('; '):
        if '=' in item:
            key, value = item.split('=', 1)
            cookies[key] = value
    
    # 主页面URL
    url = "https://www.priceless.com/filter/options/contentType/1"
    
    try:
        print("正在请求主页面获取总数目...")
        response = requests.get(url, headers=headers, cookies=cookies)
        print(f"主页面响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print("主页面请求成功！")
            
            # 尝试多种模式匹配总数目
            patterns = [
                r'# (\d+) Results',  # 基于网页搜索结果的格式
                r'(\d+)\s+Results',
                r'"totalTileCnt":\s*"(\d+)"',
                r'"totalTileCnt":\s*(\d+)',
                r'totalTileCnt["\']?\s*[:=]\s*["\']?(\d+)',
                r'g_block_pCnt\s*=\s*["\'](\d+)["\']',  # 从JS变量中获取
                r'loadedTitleCnt["\']?\s*[:=]\s*["\']?(\d+)',
                r'total.*?(\d+)',
            ]
            
            total_count = None
            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"模式 {i+1} 找到匹配: {matches}")
                    # 取最大的数字作为总数目
                    numbers = [int(m) for m in matches]
                    potential_total = max(numbers)
                    if potential_total > 100:  # 假设总数目应该大于100
                        total_count = potential_total
                        print(f"使用模式 {i+1} 找到总数目: {total_count}")
                        break
            
            if total_count is None:
                print("未找到总数目，搜索包含数字的文本...")
                # 查找所有数字，看看哪些可能是总数目
                all_numbers = re.findall(r'\b(\d+)\b', content)
                number_counts = {}
                for num in all_numbers:
                    if int(num) > 100 and int(num) < 10000:  # 合理的总数范围
                        number_counts[num] = number_counts.get(num, 0) + 1
                
                if number_counts:
                    print("找到的可能总数目:")
                    for num, count in sorted(number_counts.items(), key=lambda x: int(x[0]), reverse=True):
                        print(f"  数字 {num} 出现 {count} 次")
                    
                    # 使用最大的数字作为总数目
                    total_count = int(max(number_counts.keys(), key=int))
                    print(f"选择最大数字作为总数目: {total_count}")
            
            # 保存页面内容用于调试
            with open('page_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("页面内容已保存到 page_content.html")
            
            return total_count
            
        else:
            print(f"主页面请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return None
            
    except Exception as e:
        print(f"请求主页面时出错: {e}")
        return None

def main():
    scraper = PricelessScraper()
    
    # 抓取所有数据
    all_data = scraper.scrape_all_data()
    
    # 保存数据
    if all_data:
        scraper.save_data(all_data)
        
        # 打印一些统计信息
        print(f"\n统计信息:")
        print(f"总数据条数: {len(all_data)}")
        
        # 打印前几条数据的标题（如果有的话）
        print(f"\n前5条数据预览:")
        for i, item in enumerate(all_data[:5]):
            if isinstance(item, dict):
                title = item.get('title', item.get('name', '无标题'))
                print(f"{i+1}. {title}")
    else:
        print("没有获取到数据")

if __name__ == "__main__":
    # 选择运行模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 只测试获取总数目
        total_count = get_total_count_from_page()
        if total_count:
            print(f"\n最终获取到的总数目: {total_count}")
        else:
            print("\n无法获取总数目")
    else:
        # 运行完整爬虫
        main()
from lxml import html

with open('temp/html/baidu_home_raw_clean_fixed.html', 'r', encoding='utf-8') as f:
    content = f.read()

tree = html.fromstring(content)

# 检查文件总体结构
print(f'文件总长度: {len(content)} 字符')
print(f'根元素: {tree.tag}')
print(f'根元素的子元素数量: {len(tree)}')

# 查找所有元素
all_elements = tree.xpath('//*')
print(f'总元素数量: {len(all_elements)}')

# 查找表单
forms = tree.xpath('//form')
print(f'找到 {len(forms)} 个表单')

for i, form in enumerate(forms):
    print(f'表单 {i+1}: id={form.get("id", "无ID")} action={form.get("action", "无action")}')
    inputs = form.xpath('.//input')
    print(f'  包含 {len(inputs)} 个输入元素')
    for inp in inputs[:5]:
        print(f'    - type={inp.get("type", "text")} name={inp.get("name", "")} id={inp.get("id", "")} value={inp.get("value", "")}')
    print()

# 查找搜索相关的输入框
search_inputs = tree.xpath('//input[@name="wd" or @id="kw"]')
print(f'找到 {len(search_inputs)} 个搜索输入框')
for inp in search_inputs:
    print(f'  - name={inp.get("name", "")} id={inp.get("id", "")} placeholder={inp.get("placeholder", "")}')

# 查找搜索按钮
search_buttons = tree.xpath('//input[@type="submit" and contains(@value, "百度")]')
print(f'找到 {len(search_buttons)} 个搜索按钮')
for btn in search_buttons:
    print(f'  - value={btn.get("value", "")} id={btn.get("id", "")}')

# 检查是否在textarea中
textareas = tree.xpath('//textarea')
print(f'找到 {len(textareas)} 个textarea元素')
for ta in textareas:
    content_preview = ta.text_content()[:200] if ta.text_content() else "无内容"
    print(f'  - id={ta.get("id", "")} 内容预览: {content_preview}...')

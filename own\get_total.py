import requests


headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Referer": "https://www.priceless.com/filter/options/contentType/1",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
cookies = {
    "OptanonAlertBoxClosed": "2025-06-04T04:14:18.548Z",
    "AMCV_919F3704532951060A490D44%40AdobeOrg": "179643557%7CMCMID%7C37995462196494767108015999227060995520%7CvVersion%7C5.5.0",
    "_fbp": "fb.1.1749010461117.960934812518646035",
    "stk": "Sk03OHdwZlZpR0hhWXFjMUU4aGpsTkR1N35LZ2N2UzT7wwLaf0M_TNPvKW2FF9wwOj2mpgQtdpBDyS0_ahi9ow%3D%3D",
    "S": "QjB2RzsSFfy0s5OeQbm8",
    "OptanonConsent": "isGpcEnabled=0&datestamp=Wed+Jul+02+2025+17%3A24%3A35+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202505.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=d37fdb49-1ca9-458c-85d6-99d4d383fd64&interactionCount=2&isAnonUser=1&landingPath=NotLandingPage&groups=C015%3A1%2CC048%3A1%2CC076%3A1%2CC0001%3A1%2CC006%3A1%2CC0002%3A1%2CC054%3A1%2CC0003%3A1%2CC011%3A1%2CC020%3A1%2CC0004%3A1&AwaitingReconsent=false&intType=1&geolocation=CN%3BSH",
    "gpv_pn": "search.filterpage",
    "s_ips": "314",
    "s_tp": "1723",
    "s_ppv": "search.filterpage%2C18%2C18%2C314%2C1%2C5",
    "s_cc": "true",
    "s_plt": "8.60",
    "s_pltp": "search.filterpage",
    "s_nr365": "1751448323491-Repeat",
    "s_sq": "masterc611%252Cmastercglobal%3D%2526c.%2526a.%2526activitymap.%2526page%253Dsearch.filterpage%2526link%253DEnglish%2526region%253Dlanguage-scroll%2526pageIDType%253D1%2526.activitymap%2526.a%2526.c%2526pid%253Dsearch.filterpage%2526pidt%253D1%2526oid%253DEnglish%2526oidt%253D3%2526ot%253DSUBMIT",
    "C": "46-1-92-1-0",
    "P": "%2B52gOhPF%2BTyI%2F2BY5wU%2Fv%2BaxVnQuJOUuUUgoyP5a0Hzhh%2Blmu%2FtnGoCdLRN9X6zSOM%2FUo4eCq%2FfZrA2BryZ0qmF1dO1XY%2FQ7i87%2Bckfd9FjQz%2B879ZFmJApgN2%2BPqo6e%2Fen-US%7C___%7C0"
}
url = "https://www.priceless.com/filter/options/contentType/1"
response = requests.get(url, headers=headers, cookies=cookies)

with open("priceless_total.html", "w", encoding="utf-8") as f:
    f.write(response.text)

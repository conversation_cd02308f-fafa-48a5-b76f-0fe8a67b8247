import re
import os
import logging
from openai import OpenAI
from config import trip_baseUrl, trip_apikey, logger, DEFAULT_MODEL_NAME

# 设置日志
logger = logging.getLogger(__name__)

# 尝试创建OpenAI客户端并捕获可能出现的错误
try:
    client = OpenAI(api_key=trip_apikey, base_url=trip_baseUrl)
    logger.info(f"OpenAI客户端初始化成功，使用API基础URL: {trip_baseUrl}")
except Exception as e:
    logger.error(f"OpenAI客户端初始化失败: {str(e)}")
    client = None


def extract_code(text):
    """
    从文本中提取Python代码块
    """
    pattern = r'```python\s*([\s\S]*?)```'
    matches = re.search(pattern, text)
    return matches.group(1) if matches else ""


def extract_urls(text):
    """
    从文本中提取URL
    """
    # URL匹配模式
    url_pattern = r'https?://(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)'

    # 查找所有匹配的URL
    urls = re.findall(url_pattern, text)
    return urls


def optimize_prompt(model, messages):
    """
    优化提示词
    """
    from config import functions
    try:
        if client is None:
            raise ValueError("OpenAI客户端未初始化")

        completion = client.chat.completions.create(
            model=model,
            messages=messages,
            functions=functions,
            function_call="auto"
        )
        return completion.choices[0].message.content
    except Exception as e:
        logger.error(f"优化提示词时出错: {str(e)}")
        return f"API调用失败: {str(e)}"


def execute_function(function_name, args):
    """
    执行指定的函数

    Args:
        function_name (str): 要执行的函数名称
        args (dict): 函数参数

    Returns:
        dict: 函数执行结果

    Raises:
        ValueError: 当函数不存在或参数无效时
    """
    # 导入函数模块
    from functions import (
        open_and_save_page, open_and_save_page_with_remote_browser,
        clean_html, get_playwright_code, execute_playwright_code,
        edit_code, list_directory_contents, read_file_content,
        check_file_exists, delete_files
    )

    # 函数映射字典
    function_map = {
        "open_and_save_page": open_and_save_page,
        "open_and_save_page_with_remote_browser": open_and_save_page_with_remote_browser,
        "clean_html": clean_html,
        "get_playwright_code": get_playwright_code,
        "execute_playwright_code": execute_playwright_code,
        "edit_code": edit_code,
        "list_directory_contents": list_directory_contents,
        "read_file_content": read_file_content,
        "check_file_exists": check_file_exists,
        "delete_files": delete_files
    }

    try:
        # 检查函数是否存在
        if function_name not in function_map:
            raise ValueError(f"未知的函数: {function_name}")

        # 获取函数对象
        func = function_map[function_name]

        # 兼容性处理：有些地方使用function_args，有些地方使用args
        if isinstance(args, dict) and 'function_args' in args and isinstance(args['function_args'], dict):
            args = args['function_args']

        # 验证参数
        if not isinstance(args, dict):
            raise ValueError(f"参数必须是字典类型，当前类型: {type(args)}")

        # 打印参数用于调试
        logger.info(f"执行函数 {function_name} 的参数: {args}")

        # 获取函数文档中的参数信息
        import inspect
        sig = inspect.signature(func)
        required_params = [param.name for param in sig.parameters.values()
                           if param.default == inspect.Parameter.empty]

        # 检查必需参数
        missing_params = [param for param in required_params if param not in args]
        if missing_params:
            raise ValueError(f"缺少必需参数: {', '.join(missing_params)}")

        # 执行函数
        try:
            result = func(**args)
            return result

        except Exception as e:
            error_msg = f"执行函数 {function_name} 时出错: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

    except Exception as e:
        error_msg = f"函数调用失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def read_file_text(filename):
    # 构造文件路径（当前目录）
    file_path = os.path.join(os.getcwd(), filename)
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        print(f"文件 {filename} 未找到。")
    except UnicodeDecodeError:
        print(f"读取文件 {filename} 时出现编码错误，请检查文件编码。")
    except Exception as e:
        print(f"读取文件时发生异常：{e}")
/**
 * Socket.IO 事件处理器
 * 处理与服务器的实时通信
 */

// 全局变量
let socket = null;
let currentFunction = null;
let currentCodeFilePath = null;
let isTaskRunning = false;

/**
 * 初始化Socket.IO连接和事件监听
 */
function initSocketHandlers() {
    // 创建Socket.IO连接
    socket = io();

    // 连接事件
    socket.on('connect', function() {
        console.log('[Socket Event] Connected to server');
        addLog('已连接到服务器', 'success');
    });

    socket.on('disconnect', function() {
        console.log('[Socket Event] Disconnected from server');
        addLog('与服务器断开连接', 'error');
    });

    // 错误处理
    socket.on('error', function(data) {
        console.log('[Socket Event] Error:', data);
        addLog(data.message, 'error');

        // 将错误信息添加到历史记录中
        addToHistory('assistant', `错误: ${data.message}`, 'error');

        // 显示继续执行的提示
        if (data.message.includes('函数') && data.message.includes('出错')) {
            setTimeout(function() {
                addLog('正在分析错误并尝试继续执行...', 'info');
            }, 1000);
        }
    });

    // 进度更新
    socket.on('progress', function(data) {
        // 只处理不含"正在执行函数"的消息，避免重复
        if(!data.message.includes('正在执行函数：')) {
            addLog(data.message);
        }

        // 如果收到进度更新，说明任务正在运行
        if (!isTaskRunning) {
            isTaskRunning = true;
            showStopButton();
        }
    });

    // 任务完成
    socket.on('complete', function(data) {
        addLog(data.message, 'success');

        // 更新任务状态
        isTaskRunning = false;
        hideStopButton();

        // 显示继续交互的卡片
        document.getElementById('continueInteractionCard').style.display = 'block';

        // 添加任务完成的提示音
        try {
            const audio = new Audio('data:audio/mp3;base64,SUQzAwAAAAAHdlRJVDIAAAAZAAAAaHR0cDovL3d3dy5mcmVlc2Z4LmNvLnVrVEVOQwAAABUAAAhjb3B5cmlnaHQgZnJlZXNmeC5jby51a1RQRTEAAAAbAAAAU291bmQgRWZmZWN0cyAtIEZyZWVzZnguY28udWv/+1JJAAADEGFydGlzdAAAAAtwdWJsaWMgZG9tYWluL/9Uyw8AD3wgAAAoU1QcUSwOCvABr8ABhQoUKKFChQggQIECBAgQIECChQoUKFChRQoUUKKFChQoUKFCihQooUKKFChQoUKLChQoUKLChQYYKFChQoUKFChQwUKFCiRQoUKFChEAAAA=');
            audio.play();
        } catch (e) {
            console.error('播放提示音失败', e);
        }

        // 添加醒目提示消息
        addLog('🎉 任务已完成！您可以在下方输入框继续与AI进行交互', 'success');

        // 滚动到继续交互卡片
        setTimeout(function() {
            document.getElementById('continueInteractionCard').scrollIntoView({ behavior: 'smooth' });
        }, 500);
    });

    // 优化提示词
    socket.on('optimized_prompt', function(data) {
        console.log('[Socket Event] Received optimized prompt:', data);

        // 格式化优化后的提示词
        let formattedPrompt = formatPrompt(data.optimized_prompt);

        // 设置到DOM元素
        document.getElementById('optimizedPrompt').innerHTML = formattedPrompt;

        // 重置表单状态
        document.getElementById('hasOptimizedComment').checked = false;
        document.getElementById('optimizedCommentDiv').classList.add('hidden');
        document.getElementById('optimizedComment').value = '';

        // 不再弹出模态框，直接进入后续流程
        // const optimizedPromptModal = new bootstrap.Modal(document.getElementById('optimizedPromptModal'));
        // optimizedPromptModal.show();
    });

    // 函数调用
    socket.on('function_call', function(data) {
        console.log('[Socket Event] Received function call:', data);
        console.log('[Debug] Function name:', data.function_name);
        console.log('[Debug] Function args:', data.function_args);
        console.log('[Debug] Function description:', data.description);

        // 更新任务状态并显示停止按钮
        isTaskRunning = true;
        showStopButton();

        // 当收到函数调用时隐藏继续交互卡片
        document.getElementById('continueInteractionCard').style.display = 'none';

        // 如果开启了自动确认，直接确认执行
        if (document.getElementById('autoConfirmFunction').checked) {
            console.log('[Auto Confirm] Auto confirming function:', data.function_name);
            console.log('[Auto Confirm] Function args:', data.function_args);

            socket.emit('confirm_function', {
                function_name: data.function_name,
                function_args: data.function_args
            });
            addLog(`自动确认执行函数：${data.function_name}`);
            return;
        }

        // 否则显示确认对话框
        document.getElementById('functionName').textContent = data.function_name;
        document.getElementById('functionDescription').textContent = data.description || '无描述';

        // 安全地显示函数参数
        try {
            // 确保参数是对象并且可以被JSON化
            let args = data.function_args;
            if (typeof args === 'string') {
                try {
                    args = JSON.parse(args);
                } catch(e) {
                    console.error('无法解析函数参数JSON字符串:', e);
                }
            }

            // 预处理：针对URL格式问题，特殊处理url参数
            if (args && typeof args === 'object') {
                // 检查常见的URL参数字段
                const urlFields = ['url', 'save_name', 'html_path', 'file_path', 'code_file_path'];
                urlFields.forEach(field => {
                    if (args[field] && typeof args[field] === 'string') {
                        // 检查并修复URL字段可能的格式问题
                        args[field] = args[field].replace(/"\s+target="_blank"\s+class="[^"]+">/g, '');
                    }
                });
            }

            // 格式化显示
            let formattedArgs = JSON.stringify(args, null, 2);

            // 安全处理：先转义HTML，然后处理URL
            formattedArgs = formattedArgs
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;");

            // 处理URL显示，使链接可点击
            formattedArgs = formattedArgs.replace(/"(https?:\/\/[^"]+)"/g, function(match, url) {
                // 确保URL是安全的
                const safeUrl = url
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");

                return '"<a href="' + safeUrl + '" target="_blank">' + safeUrl + '</a>"';
            });

            document.getElementById('functionArgs').innerHTML = formattedArgs;
        } catch(e) {
            console.error('处理函数参数时出错:', e);
            // 回退到纯文本显示，确保不会有渲染错误
            const plainArgs = JSON.stringify(data.function_args, null, 2);
            document.getElementById('functionArgs').textContent = plainArgs;
        }

        currentFunction = {
            name: data.function_name,
            args: data.function_args
        };

        document.getElementById('hasFunctionComment').checked = false;
        document.getElementById('functionCommentDiv').classList.add('hidden');
        document.getElementById('functionComment').value = '';
        
        const functionModal = new bootstrap.Modal(document.getElementById('functionModal'));
        functionModal.show();

        addLog(`等待确认执行函数：${data.function_name}`);
    });

    // 执行代码
    socket.on('execute_code', function(data) {
        document.getElementById('codeFilePath').textContent = data.code_file_path;
        currentCodeFilePath = data.code_file_path;
        const executeModal = new bootstrap.Modal(document.getElementById('executeModal'));
        executeModal.show();
    });

    // 代码重试
    socket.on('code_retry', function(data) {
        addLog(`代码生成重试 (${data.retry_count}/${data.max_retries})：${data.reason}`);
    });

    // 消息清空
    socket.on('messages_cleared', function() {
        // 清空进度步骤
        const progressSteps = document.getElementById('progressSteps');
        if (progressSteps) {
            progressSteps.innerHTML = '';
        }

        // 重置任务状态
        isTaskRunning = false;
        hideStopButton();

        // 显示成功反馈
        showSuccessFeedback('✅ 上下文已清除，可以开始新的对话');
        addLog('消息历史已清空', 'success');
    });

    // 消息删除
    socket.on('message_deleted', function(data) {
        // 这个功能在当前页面中不适用，因为没有messageHistory元素
        console.log('Message deleted:', data);
    });

    // 任务停止
    socket.on('task_stopped', function(data) {
        console.log('[Socket Event] Task stopped:', data);

        // 更新任务状态
        isTaskRunning = false;
        hideStopButton();

        // 显示成功反馈
        showSuccessFeedback('🛑 任务已成功停止');
        addLog(data.message, 'warning');

        // 显示继续交互的卡片
        document.getElementById('continueInteractionCard').style.display = 'block';

        // 如果有详细信息，也显示出来
        if (data.details) {
            addLog(data.details, 'info');
        }
    });
}

/**
 * 发送任务到服务器
 * @param {string} prompt - 任务描述
 */
function sendTaskToServer(prompt) {
    if (socket && prompt) {
        addLog('提交任务：' + prompt);
        // 隐藏继续交互卡片
        document.getElementById('continueInteractionCard').style.display = 'none';
        socket.emit('start_automation', { prompt });
    } else {
        addLog('无法发送任务，请检查连接或任务描述', 'error');
    }
}

/**
 * 确认优化后的提示词
 * @param {string} promptText - 优化后的提示词文本
 * @param {boolean} hasComment - 是否有用户意见
 * @param {string} userComment - 用户意见内容
 */
function confirmOptimizedPrompt(promptText, hasComment, userComment) {
    if (!socket) return;

    let data = { optimized_prompt: promptText };

    if (hasComment) {
        data.has_comment = true;
        data.user_comment = userComment;
        addLog('用户对优化后的提示词提供了修改意见');
    } else {
        addLog('用户确认了优化后的提示词');
    }

    socket.emit('confirm_optimized_prompt', data);
}

/**
 * 拒绝优化后的提示词
 * @param {string} reason - 拒绝原因
 */
function rejectOptimizedPrompt(reason) {
    if (!socket) return;
    
    socket.emit('reject_optimized_prompt', {
        user_comment: reason
    });
    addLog('用户拒绝了优化后的提示词：' + reason, 'error');
}

/**
 * 确认函数调用
 * @param {string} functionName - 函数名称
 * @param {object} functionArgs - 函数参数
 * @param {boolean} hasComment - 是否有用户意见
 * @param {string} userComment - 用户意见内容
 */
function confirmFunction(functionName, functionArgs, hasComment, userComment) {
    if (!socket) return;

    let data = {
        function_name: functionName,
        function_args: functionArgs
    };

    if (hasComment) {
        data.has_comment = true;
        data.user_comment = userComment;
        addLog('用户对函数调用提供了修改意见');
    } else {
        addLog(`开始执行函数：${functionName}...`);
    }

    socket.emit('confirm_function', data);
}

/**
 * 拒绝函数调用
 * @param {string} functionName - 函数名称
 * @param {string} reason - 拒绝原因
 */
function rejectFunction(functionName, reason) {
    if (!socket) return;
    
    socket.emit('reject_function', {
        function_name: functionName,
        user_comment: reason
    });
    addLog(`用户拒绝执行函数 ${functionName}：${reason}`, 'error');
}

/**
 * 确认或拒绝执行代码
 * @param {string} codeFilePath - 代码文件路径
 * @param {boolean} confirmed - 是否确认执行
 */
function confirmExecution(codeFilePath, confirmed) {
    if (!socket) return;
    
    socket.emit('confirm_execution', {
        confirmed: confirmed,
        code_file_path: codeFilePath
    });
    
    if (confirmed) {
        addLog('用户确认执行代码：' + codeFilePath);
    } else {
        addLog('用户取消执行代码：' + codeFilePath, 'error');
    }
}

/**
 * 清空所有消息历史
 */
function clearAllMessages() {
    if (!socket) return;

    socket.emit('clear_all_messages');

    // 清空进度步骤
    const progressSteps = document.getElementById('progressSteps');
    if (progressSteps) {
        progressSteps.innerHTML = '';
    }

    // 重置任务状态
    isTaskRunning = false;
    hideStopButton();

    // 立即显示本地反馈（不等待服务器响应）
    showSuccessFeedback('🧹 正在清除上下文...');
    addLog('已清空所有消息历史');
}

/**
 * 显示停止任务按钮
 */
function showStopButton() {
    const stopBtn = document.getElementById('stopTaskBtn');
    if (stopBtn) {
        stopBtn.style.display = 'inline-block';
        // 添加闪烁效果提醒用户
        stopBtn.style.animation = 'pulse-danger 2s infinite';
    }
}

/**
 * 隐藏停止任务按钮
 */
function hideStopButton() {
    const stopBtn = document.getElementById('stopTaskBtn');
    if (stopBtn) {
        stopBtn.style.display = 'none';
    }
}

/**
 * 显示成功反馈提示
 */
function showSuccessFeedback(message) {
    // 创建反馈元素
    const feedback = document.createElement('div');
    feedback.className = 'success-feedback position-fixed';
    feedback.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 250px;
        max-width: 400px;
    `;
    feedback.innerHTML = `
        <i class="bi bi-check-circle-fill me-2"></i>
        ${message}
    `;

    // 添加到页面
    document.body.appendChild(feedback);

    // 3秒后自动移除
    setTimeout(() => {
        if (feedback.parentNode) {
            feedback.style.animation = 'slideOutToTop 0.5s ease-in forwards';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 500);
        }
    }, 3000);
}
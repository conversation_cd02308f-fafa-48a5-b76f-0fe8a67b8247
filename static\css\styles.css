body {
    background-color: #f8f9fa;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}
.card-header {
    background-color: #007bff;
    color: white;
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
    font-weight: bold;
}
.card-body {
    padding: 20px;
}
.form-control {
    border-radius: 5px;
    padding: 10px 15px;
    margin-bottom: 15px;
}
.btn {
    border-radius: 5px;
    padding: 10px 20px;
    font-weight: bold;
}
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}
.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}
.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}
#progressLog {
    max-height: 300px;
    overflow-y: auto;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    margin-bottom: 15px;
}
.log-entry {
    margin-bottom: 10px;
    padding: 8px 12px;
    background-color: white;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}
.log-entry.error {
    border-left-color: #dc3545;
    background-color: #ffe6e6;
}
.log-entry.success {
    border-left-color: #28a745;
    background-color: #e6ffe6;
}
#optimizedPromptModal .modal-dialog,
#functionModal .modal-dialog {
    max-width: 800px;
}
.hidden {
    display: none;
}
.function-args {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    font-family: monospace;
    margin-bottom: 15px;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    font-size: 0.9rem;
    line-height: 1.5;
}
.function-args a {
    color: #0066cc;
    text-decoration: underline;
    word-break: break-all;
    background-color: #f0f8ff;
    padding: 2px 4px;
    border-radius: 3px;
    display: inline-block;
    margin: 2px 0;
}
.function-args a:hover {
    text-decoration: none;
    background-color: #e8f0fe;
}
.history-list {
    list-style-type: none;
    padding: 0;
}
.history-item {
    padding: 10px;
    margin-bottom: 10px;
    background-color: white;
    border-radius: 5px;
    border-left: 4px solid #6c757d;
}
.history-item .user {
    border-left-color: #007bff;
}
.history-item .assistant {
    border-left-color: #28a745;
}
.history-item .function {
    border-left-color: #ffc107;
    font-family: monospace;
}
.history-controls {
    text-align: right;
    margin-bottom: 15px;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    color: #495057;
}
.form-text {
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 0.25rem;
}
.input-group-text {
    background-color: #e9ecef;
}
.section-number {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    margin-right: 8px;
    font-size: 0.85rem;
    font-weight: bold;
}
.form-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}
.form-section:last-child {
    border-bottom: none;
}
#promptSummary {
    min-height: 100px;
    font-size: 0.95rem;
    color: #212529;
    background-color: #f8f9fa !important;
    border: 1px solid #ced4da;
}
.required-field::after {
    content: '*';
    color: #dc3545;
    margin-left: 4px;
}

/* 优化提示词样式 */
#optimizedPrompt {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    font-size: 0.95rem;
    white-space: normal;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
}
.prompt-step {
    margin: 10px 0;
    padding: 8px 12px;
    background-color: #f0f8ff;
    border-left: 3px solid #007bff;
    border-radius: 3px;
}
.prompt-title {
    font-weight: bold;
    color: #0056b3;
    margin: 10px 0 5px 0;
    padding-top: 5px;
}
.prompt-function {
    font-family: 'Consolas', 'Source Code Pro', monospace;
    background-color: #e8f0fe;
    color: #0056b3;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}
.prompt-url {
    color: #0066cc;
    text-decoration: underline;
    word-break: break-all;
    background-color: #f0f8ff;
    padding: 2px 4px;
    border-radius: 3px;
    display: inline-block;
}
.prompt-url:hover {
    text-decoration: none;
    background-color: #e8f0fe;
}
.prompt-emphasis {
    font-weight: bold;
    color: #dc3545;
}
.prompt-keyword {
    color: #28a745;
    font-weight: 500;
}
.copy-prompt-btn {
    font-size: 0.8rem;
    padding: 2px 8px;
}
#optimizedPrompt p {
    margin-bottom: 10px;
    text-indent: 0;
}
#optimizedPrompt ul, #optimizedPrompt ol {
    padding-left: 20px;
}
#optimizedPrompt li {
    margin-bottom: 5px;
}
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* 继续交互卡片样式 */
#continueInteractionCard {
    transition: all 0.3s ease-in-out;
    transform: translateY(0);
    opacity: 1;
    animation: fadeIn 0.5s;
}

#continueInteractionCard.hidden {
    transform: translateY(20px);
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#userQuery {
    border: 1px solid #28a745;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.2);
    transition: all 0.3s ease;
}

#userQuery:focus {
    border-color: #28a745;
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.4);
}

#continueInteractionForm .btn-success {
    transition: all 0.2s ease;
}

#continueInteractionForm .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

#progressSteps {
    max-height: 200px;
    overflow-y: auto;
}

.code-block {
    background: #23272f;
    color: #f8f8f2;
    font-family: 'Fira Mono', 'Consolas', 'Menlo', 'Monaco', monospace;
    font-size: 1rem;
    border-radius: 6px;
    padding: 16px;
    max-height: 400px;
    overflow: auto;
    margin: 0;
}

/* 停止任务按钮特殊样式 */
#stopTaskBtn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    border: 2px solid #dc3545 !important;
    color: white !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
    transition: all 0.3s ease;
    animation: pulse-danger 2s infinite;
}

#stopTaskBtn:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
    border-color: #a71e2a !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.5) !important;
    transform: translateY(-1px);
}

#stopTaskBtn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.4) !important;
}

/* 停止按钮的脉动动画 */
@keyframes pulse-danger {
    0% {
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }
    50% {
        box-shadow: 0 2px 12px rgba(220, 53, 69, 0.5);
    }
    100% {
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }
}

/* 成功提示样式 */
.success-feedback {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutToTop {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px);
    }
}
from playwright.sync_api import sync_playwright
import random, time

# 隐藏playwright特征
with sync_playwright() as pw:
    # 1. 启动 Chromium 浏览器，添加参数禁用明显的自动化特征
    browser = pw.chromium.launch(
        headless=False,  # 使用有头模式更接近真实浏览器
        args=[
            "--disable-blink-features=AutomationControlled",  # 禁用浏览器自动化控制特征&#8203;:contentReference[oaicite:0]{index=0}
            "--disable-infobars",  # 禁用提示栏（如“自动化软件控制浏览器”的通知）
            "--disable-extensions",  # 禁用扩展（避免扩展影响或暴露自动化痕迹）
            "--no-first-run",  # 跳过首次运行提示
            "--enable-webgl",  # 确保启用 WebGL
            "--window-size=1920,1080"  # 设定窗口尺寸模拟常见屏幕分辨率
        ]
    )

    # 2. 创建浏览器上下文（BrowserContext），设置自定义的环境参数
    context = browser.new_context(
        user_agent=("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "  # 自定义 User-Agent，模拟常用浏览器标识
                    "AppleWebKit/537.36 (KHTML, like Gecko) "
                    "Chrome/112.0.0.0 Safari/537.36"),
        locale="en-US",  # 设置浏览器语言为英文 (影响 Accept-Language 等)
        timezone_id="Asia/Tokyo",  # 设置所在时区，模拟用户本地时间
        viewport={"width": 1920, "height": 1080},  # 设置浏览器视口大小（宽高）
        screen={"width": 1920, "height": 1080}  # 设置window.screen分辨率与视口一致
    )

    # # 3. 注入脚本以隐藏 navigator.webdriver 属性
    STEALTH_JS = """
    () => {
        const newProto = navigator.__proto__;
        delete newProto.webdriver;
        navigator.__proto__ = newProto;

        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en']
        });

        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5]
        });

        Object.defineProperty(navigator, 'webdriver', {
            get: () => false
        });

        window.chrome = {
            runtime: {}
        };

        window.navigator.chrome = {
            runtime: {}
        };
    }
    """
    context.add_init_script(STEALTH_JS)

    # 4. 注入脚本伪造硬件特征（如 GPU 信息、内存等）
    context.add_init_script("""
        // 覆盖 WebGL API 的 getParameter 方法，伪装GPU厂商和渲染器
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(param) {
            // WebGL 常量: UNMASKED_VENDOR_WEBGL = 37445, UNMASKED_RENDERER_WEBGL = 37446
            if (param === 37445) {
                return "Intel Inc.";  // 伪造显卡厂商
            }
            if (param === 37446) {
                return "Intel Iris OpenGL Engine";  // 伪造GPU渲染器名称
            }
            return getParameter.call(this, param);
        };

        // 伪装设备内存和硬件并发线程数
        Object.defineProperty(navigator, 'deviceMemory', { get: () => 8 });      // 8GB 内存
        Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 8 });  # 8核CPU
    """)

    # 5. 新建页面
    page = context.new_page()

    # 模拟打开目标网站（此处以示例站点代替），进入页面
    page.goto("https://fingerprintjs.github.io/BotD/main/")



    input("按回车键继续...")
    # 关闭浏览器
    browser.close()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Flask应用主入口文件
负责创建Flask应用、注册事件处理器和启动服务器

此文件通过导入各模块化组件，协调整个应用的运行
"""

# 标准库导入
import logging
import sys
import os
import json

# 第三方库导入
from flask import Flask, render_template, request, send_file, abort
from flask_socketio import SocketIO
from flask_cors import CORS

# 本地模块导入
from config import (
    message_history, execution_locks, function_execution_locks, task_cancellation_flags,
    TEMP_DIR, HTML_DIR, CODE_DIR, DATA_DIR,
)

from handlers import (
    handle_start_automation, handle_delete_message, handle_confirm_execution,
    handle_clear_all_messages, handle_confirm_function, handle_execute_function,
    handle_reject_function, handle_confirm_optimized_prompt, handle_reject_optimized_prompt,
    handle_stop_task
)

# 设置日志配置
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 初始化 Flask 应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
CORS(app, resources={r"/*": {"origins": "*"}})  # 允许所有来源的跨域请求

# 初始化 SocketIO
socketio = SocketIO(
    app,
    cors_allowed_origins="*",
    async_mode='threading',  # 使用 threading 模式
    logger=True,  # 启用日志
    engineio_logger=True  # 启用 engineio 日志
)


# 路由定义
@app.route('/')
def index():
    """渲染主页"""
    return render_template('index.html')


@app.route('/download')
def download():
    """下载后端生成的真实文件，file 参数为绝对或相对路径。"""
    file_path = request.args.get('file')
    if not file_path:
        return '缺少文件参数', 400
    # 只允许下载 HTML_DIR、CODE_DIR、DATA_DIR 下的文件
    allowed_dirs = [HTML_DIR, CODE_DIR, DATA_DIR]
    abs_path = os.path.abspath(file_path)
    if not any(os.path.commonpath([abs_path, os.path.abspath(d)]) == os.path.abspath(d) for d in allowed_dirs):
        return '禁止下载该路径', 403
    if not os.path.exists(abs_path):
        return '文件不存在', 404
    try:
        return send_file(abs_path, as_attachment=True)
    except Exception as e:
        return f'文件下载失败: {e}', 500


@app.route('/history')
def history():
    history_file = os.path.join(os.path.dirname(__file__), 'history.json')
    if os.path.exists(history_file):
        with open(history_file, 'r', encoding='utf-8') as f:
            history_data = json.load(f)
    else:
        history_data = []
    return render_template('history.html', history=history_data)


@app.route('/clear_history', methods=['POST'])
def clear_history():
    history_file = os.path.join(os.path.dirname(__file__), 'history.json')
    try:
        with open(history_file, 'w', encoding='utf-8') as f:
            f.write('[]')
        return {'success': True}
    except Exception as e:
        return {'success': False, 'message': str(e)}, 500


# SocketIO 事件处理
@socketio.on('start_automation')
def on_start_automation(data):
    """处理开始自动化请求"""
    try:
        handle_start_automation(data, socketio)
    except Exception as e:
        logger.error(f"处理start_automation事件时出错: {e}")
        socketio.emit('error', {'message': f'处理自动化请求时出错: {str(e)}'})


@socketio.on('delete_message')
def on_delete_message(data):
    """处理删除消息请求"""
    try:
        handle_delete_message(data, socketio)
    except Exception as e:
        logger.error(f"处理delete_message事件时出错: {e}")
        socketio.emit('error', {'message': f'删除消息时出错: {str(e)}'})


@socketio.on('confirm_execution')
def on_confirm_execution(data):
    """处理确认执行代码请求"""
    try:
        handle_confirm_execution(data, socketio)
    except Exception as e:
        logger.error(f"处理confirm_execution事件时出错: {e}")
        socketio.emit('error', {'message': f'确认执行代码时出错: {str(e)}'})


@socketio.on('clear_all_messages')
def on_clear_all_messages():
    """处理清空所有消息请求"""
    try:
        handle_clear_all_messages(socketio)
    except Exception as e:
        logger.error(f"处理clear_all_messages事件时出错: {e}")
        socketio.emit('error', {'message': f'清空消息时出错: {str(e)}'})


@socketio.on('confirm_function')
def on_confirm_function(data):
    """处理确认函数执行请求"""
    try:
        handle_confirm_function(data, socketio)
    except Exception as e:
        logger.error(f"处理confirm_function事件时出错: {e}")
        socketio.emit('error', {'message': f'确认函数执行时出错: {str(e)}'})


@socketio.on('execute_function')
def on_execute_function(data):
    """处理执行函数请求"""
    try:
        handle_execute_function(data, socketio)
    except Exception as e:
        logger.error(f"处理execute_function事件时出错: {e}")
        socketio.emit('error', {'message': f'执行函数时出错: {str(e)}'})


@socketio.on('reject_function')
def on_reject_function(data):
    """处理拒绝函数请求"""
    try:
        handle_reject_function(data, socketio)
    except Exception as e:
        logger.error(f"处理reject_function事件时出错: {e}")
        socketio.emit('error', {'message': f'拒绝函数时出错: {str(e)}'})


@socketio.on('confirm_optimized_prompt')
def on_confirm_optimized_prompt(data):
    """处理确认优化提示词请求"""
    try:
        handle_confirm_optimized_prompt(data, socketio)
    except Exception as e:
        logger.error(f"处理confirm_optimized_prompt事件时出错: {e}")
        socketio.emit('error', {'message': f'确认优化提示词时出错: {str(e)}'})


@socketio.on('reject_optimized_prompt')
def on_reject_optimized_prompt(data):
    """处理拒绝优化提示词请求"""
    try:
        handle_reject_optimized_prompt(data, socketio)
    except Exception as e:
        logger.error(f"处理reject_optimized_prompt事件时出错: {e}")
        socketio.emit('error', {'message': f'拒绝优化提示词时出错: {str(e)}'})


@socketio.on('stop_task')
def on_stop_task(data):
    """处理停止任务请求"""
    try:
        handle_stop_task(data, socketio)
    except Exception as e:
        logger.error(f"处理stop_task事件时出错: {e}")
        socketio.emit('error', {'message': f'停止任务时出错: {str(e)}'})


@socketio.on_error()
def error_handler(e):
    """统一处理SocketIO事件处理过程中的错误"""
    logger.error(f"SocketIO事件处理错误: {str(e)}")
    socketio.emit('error', {'message': f'操作失败: {str(e)}'})


@socketio.on('connect')
def handle_connect():
    """处理客户端连接事件"""
    logger.info(f"客户端已连接: {request.sid}")


@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接事件"""
    logger.info(f"客户端已断开连接: {request.sid}")
    # 清理该客户端的资源
    if request.sid in message_history:
        del message_history[request.sid]
    if request.sid in execution_locks:
        del execution_locks[request.sid]
    if request.sid in function_execution_locks:
        del function_execution_locks[request.sid]
    if request.sid in task_cancellation_flags:
        del task_cancellation_flags[request.sid]


if __name__ == '__main__':
    try:
        # 确保必要的目录存在
        for directory in [TEMP_DIR, HTML_DIR, CODE_DIR, DATA_DIR]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"已创建目录: {directory}")

        # 启动应用
        logger.info("正在启动应用...")
        socketio.run(
            app,
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False,
            allow_unsafe_werkzeug=True  # 禁用重载器以避免重复初始化
        )
    except Exception as e:
        logger.critical(f"应用启动失败: {str(e)}")
        sys.exit(1)

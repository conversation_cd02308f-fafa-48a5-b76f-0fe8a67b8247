/**
 * 主JavaScript文件
 * 处理页面初始化和事件绑定
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化Socket.IO连接
    initSocketHandlers();

    // 获取关键DOM元素
    const promptForm = document.getElementById('promptForm');
    const taskUrl = document.getElementById('taskUrl');
    const taskType = document.getElementById('taskType');
    const dataRequirements = document.getElementById('dataRequirements');
    const dataFormat = document.getElementById('dataFormat');
    const needPagination = document.getElementById('needPagination');
    const paginationOptions = document.getElementById('paginationOptions');
    const pageCount = document.getElementById('pageCount');
    const additionalRequirements = document.getElementById('additionalRequirements');
    const promptSummary = document.getElementById('promptSummary');
    const clearHistory = document.getElementById('clearHistory');
    
    // 格式选项元素
    const formatOptions = document.getElementById('formatOptions');
    const txtSeparator = document.getElementById('txtSeparator');
    const customSeparatorDiv = document.getElementById('customSeparatorDiv');
    
    // 模态框按钮
    const confirmOptimizedPromptBtn = document.getElementById('confirmOptimizedPrompt');
    const rejectOptimizedPromptBtn = document.getElementById('rejectOptimizedPrompt');
    const confirmFunctionBtn = document.getElementById('confirmFunction');
    const rejectFunctionBtn = document.getElementById('rejectFunction');
    const confirmExecutionBtn = document.getElementById('confirmExecution');
    const rejectExecutionBtn = document.getElementById('rejectExecution');
    const stopTaskBtn = document.getElementById('stopTaskBtn');
    const confirmStopTaskBtn = document.getElementById('confirmStopTask');
    
    // 高级选项按钮动画和文字切换
    const toggleBtn = document.getElementById('toggleAdvancedBtn');
    const arrowIcon = document.getElementById('advancedArrow').querySelector('i');
    const advancedOptions = document.getElementById('advancedOptions');
    // 监听展开
    advancedOptions.addEventListener('shown.bs.collapse', function() {
        arrowIcon.classList.remove('bi-chevron-down');
        arrowIcon.classList.add('bi-chevron-up');
        toggleBtn.querySelector('span:nth-child(2)').textContent = '收起高级选项';
    });
    // 监听收起
    advancedOptions.addEventListener('hidden.bs.collapse', function() {
        arrowIcon.classList.remove('bi-chevron-up');
        arrowIcon.classList.add('bi-chevron-down');
        toggleBtn.querySelector('span:nth-child(2)').textContent = '展开高级选项';
    });
    
    // 在页面加载时初始化格式选项
    updateFormatOptions();
    
    // 翻页选项显示/隐藏
    needPagination.addEventListener('change', function() {
        paginationOptions.classList.toggle('hidden', !this.checked);
        updatePromptSummary();
    });

    // 数据格式相关的事件监听
    dataFormat.addEventListener('change', function() {
        updateFormatOptions();
        updatePromptSummary();
    });

    function updateFormatOptions() {
        // 隐藏所有格式分组
        document.querySelectorAll('.format-options').forEach(el => el.classList.add('hidden'));
        // 显示当前选中的格式分组
        const format = dataFormat.value;
        if (format === 'csv') {
            document.getElementById('csvOptions').classList.remove('hidden');
        } else if (format === 'json') {
            document.getElementById('jsonOptions').classList.remove('hidden');
        } else if (format === 'excel') {
            document.getElementById('excelOptions').classList.remove('hidden');
        } else if (format === 'txt') {
            document.getElementById('txtOptions').classList.remove('hidden');
        }
    }

    // 监听文本格式中的自定义分隔符选项
    txtSeparator.addEventListener('change', function() {
        customSeparatorDiv.classList.toggle('hidden', this.value !== 'custom');
        updatePromptSummary();
    });

    // 添加所有输入元素的变化监听，用于更新任务描述
    document.querySelectorAll('#promptForm input, #promptForm textarea, #promptForm select').forEach(element => {
        if (element.tagName === 'SELECT' || element.type === 'checkbox') {
            element.addEventListener('change', updatePromptSummary);
        } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            element.addEventListener('input', updatePromptSummary);
        }
    });

    // 表单提交处理
    promptForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // 清空生成代码和结果区
        document.getElementById('generatedCode').textContent = '';
        document.getElementById('generatedResult').textContent = '';
        document.getElementById('codeCard').style.display = 'none';
        document.getElementById('resultCard').style.display = 'none';
        // 直接拼接用户输入生成 prompt
        const url = taskUrl.value.trim();
        const type = taskType.options[taskType.selectedIndex].text;
        const requirements = dataRequirements.value.trim();
        const format = dataFormat.options[dataFormat.selectedIndex].text;
        const pagination = needPagination.checked;
        const pages = pagination ? pageCount.options[pageCount.selectedIndex].text : '不需要翻页';
        const additional = additionalRequirements.value.trim();

        let prompt = '';
        if (url) prompt += `目标网址：${url}。`;
        if (type) prompt += `任务类型：${type}。`;
        if (requirements) prompt += `数据要求：${requirements}。`;
        if (format) prompt += `数据保存格式：${format}。`;
        if (pagination) prompt += `需要翻页，页数：${pages}。`;
        if (additional) prompt += `其他要求：${additional}。`;

        // 打印原始提示词
        console.log('[原始提示词]', prompt);

        if (prompt) {
            sendTaskToServer(prompt, false);
        } else {
            addLog('请填写必要的任务信息', 'error');
        }
    });

    // 优化提示词相关事件处理
    document.getElementById('hasOptimizedComment').addEventListener('change', function() {
        document.getElementById('optimizedCommentDiv').classList.toggle('hidden', !this.checked);
    });

    // 函数调用相关事件处理
    document.getElementById('hasFunctionComment').addEventListener('change', function() {
        document.getElementById('functionCommentDiv').classList.toggle('hidden', !this.checked);
    });

    // 确认优化后的提示词
    confirmOptimizedPromptBtn.addEventListener('click', function() {
        const promptText = document.getElementById('optimizedPrompt').textContent;
        const hasComment = document.getElementById('hasOptimizedComment').checked;
        const userComment = document.getElementById('optimizedComment').value;

        // 打印优化后的提示词
        console.log('[优化后的提示词]', promptText);

        confirmOptimizedPrompt(promptText, hasComment, userComment);
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('optimizedPromptModal'));
        if (modal) modal.hide();
    });

    // 拒绝优化后的提示词
    rejectOptimizedPromptBtn.addEventListener('click', function() {
        const userComment = prompt('请输入拒绝原因：', '我对优化后的提示词不满意');
        if (userComment !== null) {
            rejectOptimizedPrompt(userComment);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('optimizedPromptModal'));
            if (modal) modal.hide();
        }
    });

    // 确认函数调用
    confirmFunctionBtn.addEventListener('click', function() {
        if (!currentFunction) return;

        const hasComment = document.getElementById('hasFunctionComment').checked;
        const userComment = document.getElementById('functionComment').value;
        
        confirmFunction(currentFunction.name, currentFunction.args, hasComment, userComment);
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('functionModal'));
        if (modal) modal.hide();
    });

    // 拒绝函数调用
    rejectFunctionBtn.addEventListener('click', function() {
        if (!currentFunction) return;

        const userComment = prompt('请输入拒绝原因：', '我对此函数调用有疑虑');
        if (userComment !== null) {
            rejectFunction(currentFunction.name, userComment);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('functionModal'));
            if (modal) modal.hide();
        }
    });

    // 确认执行代码
    confirmExecutionBtn.addEventListener('click', function() {
        if (currentCodeFilePath) {
            confirmExecution(currentCodeFilePath, true);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('executeModal'));
            if (modal) modal.hide();
        }
    });

    // 拒绝执行代码
    rejectExecutionBtn.addEventListener('click', function() {
        if (currentCodeFilePath) {
            confirmExecution(currentCodeFilePath, false);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('executeModal'));
            if (modal) modal.hide();
        }
    });

    // 清空历史
    if (clearHistory) {
        clearHistory.addEventListener('click', function() {
            if (confirm('确定要清空所有消息历史吗？')) {
                clearAllMessages();
            }
        });
    }

    // 继续交互表单处理
    const continueInteractionForm = document.getElementById('continueInteractionForm');
    const userQuery = document.getElementById('userQuery');

    continueInteractionForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const query = userQuery.value.trim();
        if (!query) {
            addLog('请输入您的问题或指示', 'error');
            return;
        }

        // 增强用户输入，添加激励大模型主动调用函数的提示词
        const enhancedQuery = enhanceUserQuery(query);

        // 调试信息：在控制台显示增强后的查询
        console.log('=== 用户原始输入 ===');
        console.log(query);
        console.log('=== 增强后的查询 ===');
        console.log(enhancedQuery);
        console.log('========================');

        // 添加用户输入到历史和日志
        addLog(`用户: ${query}`, 'info');
        addToHistory('user', query);

        // 发送到后端并隐藏交互卡片
        document.getElementById('continueInteractionCard').style.display = 'none';
        sendTaskToServer(enhancedQuery, true);

        // 清空输入框
        userQuery.value = '';
    });

    // 初始化提示信息
    addLog('iCrawler-您的智能数据抓取助手已准备就绪，请输入您的任务描述', 'success');

    // 生成代码和结果展示区的socket事件
    let resultFilePath = null;
    if (typeof socket !== 'undefined') {
        socket.on('show_code', function(data) {
            document.getElementById('codeCard').style.display = '';
            document.getElementById('generatedCode').textContent = data.code || '';
            if (document.getElementById('codeCardBody')) document.getElementById('codeCardBody').style.display = '';
            saveHomeState();
        });
        socket.on('show_result', function(data) {
            document.getElementById('resultCard').style.display = '';
            let result = data.result || '';
            // 只显示文件内容预览部分，过滤掉"已保存...到:"和"数据保存位置：..."等提示
            result = result.replace(/^已保存.*到:.*\.(csv|json|txt|xlsx|xls|html|zip|parquet|tsv|dat|log|md|docx|pdf|png|jpg|jpeg|webp|gif|bmp|svg|mp4|avi|mov|mp3|wav|flac|aac|ogg|m4a|jsonl|xml|yml|yaml|ini|conf|cfg|db|sqlite|h5|hdf5|npz|npy|pkl|sav|rds|feather|orc|avro|msgpack|tar|gz|bz2|7z|rar|exe|dll|bat|sh|py|js|ts|ipynb|r|cpp|c|h|hpp|go|rs|swift|kt|java|class|jar|war|cs|vb|pl|lua|rb|php|asp|aspx|jsp|vue|react|svelte|astro|wasm|bin|dat|bak|tmp|swp|lock|log|out|err|lst|lst|csv|json|txt|xlsx|xls|html|zip|parquet|tsv|dat|log|md|docx|pdf|png|jpg|jpeg|webp|gif|bmp|svg|mp4|avi|mov|mp3|wav|flac|aac|ogg|m4a|jsonl|xml|yml|yaml|ini|conf|cfg|db|sqlite|h5|hdf5|npz|npy|pkl|sav|rds|feather|orc|avro|msgpack|tar|gz|bz2|7z|rar|exe|dll|bat|sh|py|js|ts|ipynb|r|cpp|c|h|hpp|go|rs|swift|kt|java|class|jar|war|cs|vb|pl|lua|rb|php|asp|aspx|jsp|vue|react|svelte|astro|wasm|bin|dat|bak|tmp|swp|lock|log|out|err|lst|lst)$/mg, '');
            result = result.replace(/^数据保存位置：.*$/mg, '');
            result = result.replace(/^输出文件：.*$/mg, '');
            result = result.replace(/\n{3,}/g, '\n\n');
            // 只保留"【文件内容预览】"及其下方内容
            const previewMatch = result.match(/【文件内容预览】[\s\S]*/);
            if (previewMatch) {
                result = previewMatch[0];
            } else {
                result = '';
            }
            document.getElementById('generatedResult').textContent = result.trim();
            if (document.getElementById('resultCardBody')) document.getElementById('resultCardBody').style.display = '';
            resultFilePath = data.file_path || null;
            saveHomeState();
        });
        // 进度更新监听
        socket.on('progress_update', function(data) {
            console.log('[Socket] progress_update:', data);
            const progressSteps = document.getElementById('progressSteps');
            if (progressSteps) {
                // 只在"任务开始"或"初始化"时清空
                const isStart = /开始|初始化|优化用户输入/.test(data.message);
                if (isStart) {
                    console.log('[Socket] progress_update: 清空进度区');
                    progressSteps.innerHTML = '';
                }
                const li = document.createElement('li');
                li.className = 'progress-step';
                // 判断是否为进行中还是完成
                const isProcessing = /正在|处理中|分析|执行|生成|优化/.test(data.message) && !/完成|已完成|成功|失败|已终止|已取消|已拒绝/.test(data.message);
                const isDone = /完成|已完成|成功|失败|已终止|已取消|已拒绝/.test(data.message);
                if (isProcessing) {
                    // 移除已有的"正在..."li，只保留一个
                    Array.from(progressSteps.children).forEach(child => {
                        if (child.querySelector('.spinner-border')) {
                            progressSteps.removeChild(child);
                        }
                    });
                    li.innerHTML = `<span class="spinner-border spinner-border-sm text-primary me-2" role="status"></span>${data.message}`;
                    progressSteps.appendChild(li);
                } else if (isDone) {
                    // 先移除所有"正在..."li，保证完成时没有转圈
                    Array.from(progressSteps.children).forEach(child => {
                        if (child.querySelector('.spinner-border')) {
                            progressSteps.removeChild(child);
                        }
                    });
                    li.innerHTML = `<span class="text-success"><i class="bi bi-check-circle-fill me-2"></i>${data.message.replace(/^✅\s*/, '')}</span>`;
                    progressSteps.appendChild(li);
                } else {
                    li.textContent = data.message;
                    progressSteps.appendChild(li);
                }
                progressSteps.scrollTop = progressSteps.scrollHeight;
            }
        });
        socket.on('show_optimized_prompt', function(data) {
            if (data && data.optimizedPrompt) {
                console.log('[优化后的提示词]', data.optimizedPrompt);
            }
        });
        socket.on('complete', function(data) {
            console.log('[Socket] complete:', data);
            // 显示AI回复到执行进度区
            const progressSteps = document.getElementById('progressSteps');
            if (progressSteps) {
                const li = document.createElement('li');
                li.className = 'progress-step';
                li.innerHTML = `<span class="text-primary"><i class="bi bi-chat-dots me-2"></i>${data.message || ''}</span>`;
                progressSteps.appendChild(li);
                progressSteps.scrollTop = progressSteps.scrollHeight;
            }
            // 追加到历史区（如有）
            if (typeof addLog === 'function') {
                addLog('AI: ' + (data.message || ''), 'info');
            }
            if (typeof addToHistory === 'function') {
                addToHistory('ai', data.message || '');
            }
        });
    }
    // 复制按钮
    const copyCodeBtn = document.getElementById('copyCodeBtn');
    if (copyCodeBtn) {
        copyCodeBtn.onclick = function() {
            const code = document.getElementById('generatedCode').textContent;
            navigator.clipboard.writeText(code);
        };
    }
    // 代码下载按钮
    const downloadCodeBtn = document.getElementById('downloadCodeBtn');
    if (downloadCodeBtn) {
        downloadCodeBtn.onclick = function() {
            const code = document.getElementById('generatedCode').textContent;
            const blob = new Blob([code], {type: 'text/x-python'});
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = 'generated_code.py';
            a.click();
            URL.revokeObjectURL(a.href);
        };
    }
    // 结果下载按钮
    const downloadResultBtn = document.getElementById('downloadResultBtn');
    if (downloadResultBtn) {
        downloadResultBtn.onclick = function() {
            if (resultFilePath) {
                window.open('/download?file=' + encodeURIComponent(resultFilePath));
            } else {
                const result = document.getElementById('generatedResult').textContent;
                const blob = new Blob([result], {type: 'text/plain'});
                const a = document.createElement('a');
                a.href = URL.createObjectURL(blob);
                a.download = 'generated_result.txt';
                a.click();
                URL.revokeObjectURL(a.href);
            }
        };
    }
    // 结果复制按钮
    const copyResultBtn = document.getElementById('copyResultBtn');
    if (copyResultBtn) {
        copyResultBtn.onclick = function() {
            const result = document.getElementById('generatedResult').textContent;
            navigator.clipboard.writeText(result);
        };
    }

    // 展开/收起 生成代码卡片
    document.getElementById('toggleCodeCardBtn').onclick = function() {
        const body = document.getElementById('codeCardBody');
        const arrow = document.getElementById('codeCardArrow');
        if (body.style.display === 'none') {
            body.style.display = '';
            arrow.classList.remove('bi-chevron-down');
            arrow.classList.add('bi-chevron-up');
        } else {
            body.style.display = 'none';
            arrow.classList.remove('bi-chevron-up');
            arrow.classList.add('bi-chevron-down');
        }
    };
    // 展开/收起 生成结果卡片
    document.getElementById('toggleResultCardBtn').onclick = function() {
        const body = document.getElementById('resultCardBody');
        const arrow = document.getElementById('resultCardArrow');
        if (body.style.display === 'none') {
            body.style.display = '';
            arrow.classList.remove('bi-chevron-down');
            arrow.classList.add('bi-chevron-up');
        } else {
            body.style.display = 'none';
            arrow.classList.remove('bi-chevron-up');
            arrow.classList.add('bi-chevron-down');
        }
    };

    // 本地存储相关常量
    const STORAGE_KEY = 'icrawler_home_state_v1';

    // 保存主页状态到localStorage
    function saveHomeState() {
        const state = {
            taskUrl: taskUrl.value,
            taskType: taskType.value,
            dataRequirements: dataRequirements.value,
            dataFormat: dataFormat.value,
            // 高级选项
            csvColumns: document.getElementById('csvColumns')?.value || '',
            csvIncludeHeader: document.getElementById('csvIncludeHeader')?.checked || false,
            csvEncoding: document.getElementById('csvEncoding')?.value || '',
            jsonStructure: document.getElementById('jsonStructure')?.value || '',
            jsonFields: document.getElementById('jsonFields')?.value || '',
            jsonPretty: document.getElementById('jsonPretty')?.checked || false,
            excelColumns: document.getElementById('excelColumns')?.value || '',
            excelSheetName: document.getElementById('excelSheetName')?.value || '',
            excelAutoFilter: document.getElementById('excelAutoFilter')?.checked || false,
            excelFreeze: document.getElementById('excelFreeze')?.checked || false,
            txtSeparator: document.getElementById('txtSeparator')?.value || '',
            customSeparator: document.getElementById('customSeparator')?.value || '',
            txtEncoding: document.getElementById('txtEncoding')?.value || '',
            needPagination: needPagination.checked,
            pageCount: pageCount.value,
            additionalRequirements: additionalRequirements.value,
            // 代码和结果区
            code: document.getElementById('generatedCode')?.textContent || '',
            result: document.getElementById('generatedResult')?.textContent || '',
            codeCardVisible: document.getElementById('codeCard')?.style.display !== 'none',
            resultCardVisible: document.getElementById('resultCard')?.style.display !== 'none',
        };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    }

    // 恢复主页状态
    function restoreHomeState() {
        const stateStr = localStorage.getItem(STORAGE_KEY);
        // 卡片始终显示，内容区默认折叠
        if (document.getElementById('codeCard')) document.getElementById('codeCard').style.display = '';
        if (document.getElementById('resultCard')) document.getElementById('resultCard').style.display = '';
        if (document.getElementById('codeCardBody')) document.getElementById('codeCardBody').style.display = 'none';
        if (document.getElementById('resultCardBody')) document.getElementById('resultCardBody').style.display = 'none';
        if (!stateStr) {
            return;
        }
        try {
            const state = JSON.parse(stateStr);
            if (state.taskUrl !== undefined) taskUrl.value = state.taskUrl;
            if (state.taskType !== undefined) taskType.value = state.taskType;
            if (state.dataRequirements !== undefined) dataRequirements.value = state.dataRequirements;
            if (state.dataFormat !== undefined) dataFormat.value = state.dataFormat;
            // 触发格式选项显示
            updateFormatOptions();
            // 高级选项
            if (document.getElementById('csvColumns')) document.getElementById('csvColumns').value = state.csvColumns || '';
            if (document.getElementById('csvIncludeHeader')) document.getElementById('csvIncludeHeader').checked = !!state.csvIncludeHeader;
            if (document.getElementById('csvEncoding')) document.getElementById('csvEncoding').value = state.csvEncoding || 'utf-8';
            if (document.getElementById('jsonStructure')) document.getElementById('jsonStructure').value = state.jsonStructure || 'records';
            if (document.getElementById('jsonFields')) document.getElementById('jsonFields').value = state.jsonFields || '';
            if (document.getElementById('jsonPretty')) document.getElementById('jsonPretty').checked = !!state.jsonPretty;
            if (document.getElementById('excelColumns')) document.getElementById('excelColumns').value = state.excelColumns || '';
            if (document.getElementById('excelSheetName')) document.getElementById('excelSheetName').value = state.excelSheetName || '数据';
            if (document.getElementById('excelAutoFilter')) document.getElementById('excelAutoFilter').checked = !!state.excelAutoFilter;
            if (document.getElementById('excelFreeze')) document.getElementById('excelFreeze').checked = !!state.excelFreeze;
            if (document.getElementById('txtSeparator')) document.getElementById('txtSeparator').value = state.txtSeparator || '|';
            if (document.getElementById('customSeparator')) document.getElementById('customSeparator').value = state.customSeparator || '';
            if (document.getElementById('txtEncoding')) document.getElementById('txtEncoding').value = state.txtEncoding || 'utf-8';
            if (needPagination) needPagination.checked = !!state.needPagination;
            if (paginationOptions) paginationOptions.classList.toggle('hidden', !state.needPagination);
            if (pageCount) pageCount.value = state.pageCount || '3';
            if (additionalRequirements) additionalRequirements.value = state.additionalRequirements || '';
            // 代码和结果区内容恢复
            if (document.getElementById('generatedCode')) document.getElementById('generatedCode').textContent = state.code || '';
            if (document.getElementById('generatedResult')) document.getElementById('generatedResult').textContent = state.result || '';
        } catch (e) {
            console.warn('主页状态恢复失败', e);
        }
    }

    // 主页加载时自动恢复
    restoreHomeState();

    // 监听所有输入和选项变化，自动保存
    document.querySelectorAll('#promptForm input, #promptForm textarea, #promptForm select').forEach(element => {
        if (element.tagName === 'SELECT' || element.type === 'checkbox') {
            element.addEventListener('change', saveHomeState);
        } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            element.addEventListener('input', saveHomeState);
        }
    });
    // 停止任务按钮事件
    if (stopTaskBtn) {
        stopTaskBtn.addEventListener('click', function() {
            const stopTaskModal = new bootstrap.Modal(document.getElementById('stopTaskModal'));
            stopTaskModal.show();
        });
    }

    // 确认停止任务按钮事件
    if (confirmStopTaskBtn) {
        confirmStopTaskBtn.addEventListener('click', function() {
            if (socket) {
                socket.emit('stop_task', {});
                addLog('正在停止任务...', 'warning');

                // 立即显示反馈
                if (typeof showSuccessFeedback === 'function') {
                    showSuccessFeedback('⏳ 正在停止任务...');
                }
            }
            // 关闭模态框
            const stopTaskModal = bootstrap.Modal.getInstance(document.getElementById('stopTaskModal'));
            if (stopTaskModal) {
                stopTaskModal.hide();
            }
        });
    }

    // 监听代码和结果区变化（通过socket事件）
    if (typeof socket !== 'undefined') {
        socket.on('show_code', function(data) {
            document.getElementById('codeCard').style.display = '';
            document.getElementById('generatedCode').textContent = data.code || '';
            if (document.getElementById('codeCardBody')) document.getElementById('codeCardBody').style.display = '';
            saveHomeState();
        });
        socket.on('show_result', function(data) {
            document.getElementById('resultCard').style.display = '';
            let result = data.result || '';
            // 只显示文件内容预览部分，过滤掉"已保存...到:"和"数据保存位置：..."等提示
            result = result.replace(/^已保存.*到:.*\.(csv|json|txt|xlsx|xls|html|zip|parquet|tsv|dat|log|md|docx|pdf|png|jpg|jpeg|webp|gif|bmp|svg|mp4|avi|mov|mp3|wav|flac|aac|ogg|m4a|jsonl|xml|yml|yaml|ini|conf|cfg|db|sqlite|h5|hdf5|npz|npy|pkl|sav|rds|feather|orc|avro|msgpack|tar|gz|bz2|7z|rar|exe|dll|bat|sh|py|js|ts|ipynb|r|cpp|c|h|hpp|go|rs|swift|kt|java|class|jar|war|cs|vb|pl|lua|rb|php|asp|aspx|jsp|vue|react|svelte|astro|wasm|bin|dat|bak|tmp|swp|lock|log|out|err|lst|lst|csv|json|txt|xlsx|xls|html|zip|parquet|tsv|dat|log|md|docx|pdf|png|jpg|jpeg|webp|gif|bmp|svg|mp4|avi|mov|mp3|wav|flac|aac|ogg|m4a|jsonl|xml|yml|yaml|ini|conf|cfg|db|sqlite|h5|hdf5|npz|npy|pkl|sav|rds|feather|orc|avro|msgpack|tar|gz|bz2|7z|rar|exe|dll|bat|sh|py|js|ts|ipynb|r|cpp|c|h|hpp|go|rs|swift|kt|java|class|jar|war|cs|vb|pl|lua|rb|php|asp|aspx|jsp|vue|react|svelte|astro|wasm|bin|dat|bak|tmp|swp|lock|log|out|err|lst|lst)$/mg, '');
            result = result.replace(/^数据保存位置：.*$/mg, '');
            result = result.replace(/^输出文件：.*$/mg, '');
            result = result.replace(/\n{3,}/g, '\n\n');
            // 只保留"【文件内容预览】"及其下方内容
            const previewMatch = result.match(/【文件内容预览】[\s\S]*/);
            if (previewMatch) {
                result = previewMatch[0];
            } else {
                result = '';
            }
            document.getElementById('generatedResult').textContent = result.trim();
            if (document.getElementById('resultCardBody')) document.getElementById('resultCardBody').style.display = '';
            resultFilePath = data.file_path || null;
            saveHomeState();
        });
    }
});

function sendTaskToServer(prompt, isContinue = false) {
    if (socket && prompt) {
        addLog('提交任务：' + prompt);
        document.getElementById('continueInteractionCard').style.display = 'none';
        socket.emit('start_automation', { prompt, is_continue: isContinue });
    } else {
        addLog('无法发送任务，请检查连接或任务描述', 'error');
    }
}

/**
 * 增强用户查询，添加激励大模型主动调用函数的提示词
 * @param {string} userInput - 用户原始输入
 * @returns {string} - 增强后的查询
 */
function enhanceUserQuery(userInput) {
    // 定义激励提示词模板
    const enhancementPrompts = [
        "请根据用户需求立即执行相应操作，如需调用函数请直接调用，不要询问确认。",
        "用户需求如下，请主动分析并执行必要的函数调用来完成任务：",
        "请积极响应用户需求，直接调用相关函数执行操作，减少询问步骤：",
        "基于用户输入，请主动判断并执行相应的函数调用来满足需求：",
        "请立即分析用户需求并执行相应操作，优先使用function_call而非询问："
    ];

    // 定义具体的行为激励词
    const actionPrompts = {
        // 数据处理相关
        analyze: "请立即分析数据并调用相应的数据处理函数。",
        process: "请直接处理数据，调用必要的函数完成操作。",
        export: "请立即执行数据导出操作，调用相关函数。",
        save: "请直接保存数据，调用文件保存函数。",

        // 代码相关
        code: "请立即生成或修改代码，调用代码生成函数。",
        execute: "请直接执行代码，调用代码执行函数。",
        run: "请立即运行相关操作，调用执行函数。",

        // 文件操作相关
        file: "请直接进行文件操作，调用相关文件处理函数。",
        read: "请立即读取文件，调用文件读取函数。",
        write: "请直接写入文件，调用文件写入函数。",

        // 网页抓取相关
        crawl: "请立即开始网页抓取，调用相关爬虫函数。",
        scrape: "请直接抓取网页数据，调用抓取函数。",
        fetch: "请立即获取数据，调用数据获取函数。",

        // 通用操作
        help: "请根据用户问题直接提供解决方案并执行相关操作。",
        show: "请立即展示相关内容，调用显示函数。",
        check: "请直接检查并调用相关验证函数。"
    };

    // 检测用户输入中的关键词并选择合适的激励词
    let specificPrompt = "";
    const lowerInput = userInput.toLowerCase();

    for (const [keyword, prompt] of Object.entries(actionPrompts)) {
        if (lowerInput.includes(keyword) || lowerInput.includes(keyword.substring(0, 3))) {
            specificPrompt = prompt;
            break;
        }
    }

    // 随机选择一个基础激励提示词
    const basePrompt = enhancementPrompts[Math.floor(Math.random() * enhancementPrompts.length)];

    // 构建增强后的查询
    let enhancedQuery = `${basePrompt}\n\n用户需求：${userInput}\n\n`;

    // 如果有特定的行为激励词，添加它
    if (specificPrompt) {
        enhancedQuery += `特别要求：${specificPrompt}\n\n`;
    }

    // 添加通用的执行指导
    enhancedQuery += `执行指导：
1. 请主动分析用户需求，直接调用相应的函数来完成任务
2. 优先使用function_call方式执行操作，避免过多询问
3. 如果需要多个步骤，请依次调用相关函数
4. 如果遇到问题，请尝试调用相关函数解决，而不是仅仅报告问题
5. 请以实际行动响应用户需求，而不是仅提供建议

请立即开始执行！`;

    return enhancedQuery;
}
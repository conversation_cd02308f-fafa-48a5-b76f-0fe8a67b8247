from playwright.sync_api import sync_playwright
import time

def main():
    """简化版本的语言切换测试"""
    with sync_playwright() as playwright:
        # 启动浏览器
        browser = playwright.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            print("访问Priceless页面...")
            page.goto("https://www.priceless.com/filter/options/contentType/1", wait_until='networkidle')
            time.sleep(3)
            
            # 处理cookie
            print("处理cookie...")
            try:
                cookie_button = page.locator('button:has-text("接受")').first
                if cookie_button.is_visible():
                    cookie_button.click()
                    print("✅ cookie已处理")
                    time.sleep(2)
            except:
                print("ℹ️ 未找到cookie弹窗")
            
            # 第一次点击小地球
            print("\n=== 第一次点击小地球 ===")
            globe = page.locator('img[src*="globe"]').first
            if globe.is_visible():
                globe.click()
                print("✅ 第一次点击小地球成功")
                time.sleep(3)
            else:
                print("❌ 未找到小地球图标")
                return
            
            # 尝试多种方式找到语言选择按钮
            print("\n=== 寻找语言选择按钮 ===")
            language_selectors = [
                'button.current-language',
                'button:has-text("中文")',
                'button:has-text("Chinese")',
                'button[class*="language"]',
                '.language-selector button',
                '#language-dropdown button'
            ]
            
            lang_button = None
            for selector in language_selectors:
                try:
                    btn = page.locator(selector).first
                    if btn.is_visible():
                        print(f"✅ 找到语言按钮: {selector}")
                        lang_button = btn
                        break
                except:
                    continue
            
            if lang_button:
                print("点击语言选择按钮...")
                try:
                    # 使用强制点击
                    lang_button.click(force=True)
                    print("✅ 语言按钮点击完成")
                    print("ℹ️ 预期现在会退回到小地球状态...")
                    time.sleep(3)
                except Exception as e:
                    print(f"点击失败，尝试JavaScript点击: {e}")
                    page.evaluate("document.querySelector('button.current-language').click()")
                    time.sleep(3)
            else:
                print("❌ 未找到语言选择按钮，尝试直接进入下一步...")
            
            # 现在应该已经退回到小地球状态，重新点击小地球
            print("\n=== 重新点击小地球（因为已退回小地球状态）===")
            globe2 = page.locator('img[src*="globe"]').first
            if globe2.is_visible():
                globe2.click()
                print("✅ 重新点击小地球成功")
                time.sleep(3)
            else:
                print("❌ 重新点击时未找到小地球图标")
                return
            
            # 现在应该直接能看到语言选择菜单了
            print("\n=== 等待语言选择菜单出现 ===")
            try:
                # 尝试多种菜单选择器
                menu_selectors = [
                    '#language-scroll',
                    '.language-menu',
                    '[class*="language"]',
                    'button[lang-id="1"]'
                ]
                
                menu_found = False
                for selector in menu_selectors:
                    try:
                        page.wait_for_selector(selector, state='visible', timeout=3000)
                        print(f"✅ 语言菜单出现了！使用选择器: {selector}")
                        menu_found = True
                        break
                    except:
                        continue
                
                if menu_found:
                    # 直接点击English
                    print("\n=== 点击English ===")
                    english_selectors = [
                        'button[lang-id="1"]',
                        'button:has-text("English")',
                        'a:has-text("English")',
                        '[data-lang="en"]'
                    ]
                    
                    english_clicked = False
                    for selector in english_selectors:
                        try:
                            english_btn = page.locator(selector).first
                            if english_btn.is_visible():
                                english_btn.click()
                                print(f"✅ English点击成功，使用选择器: {selector}")
                                english_clicked = True
                                break
                        except:
                            continue
                    
                    if english_clicked:
                        time.sleep(5)
                        
                        # 验证语言切换
                        html_lang = page.locator('html').get_attribute('lang')
                        print(f"页面语言: {html_lang}")
                        
                        if html_lang == 'en-US':
                            print("🎉 语言切换成功！")
                        else:
                            print("❌ 语言切换可能失败")
                            
                        # 截图保存
                        page.screenshot(path="language_switched.png")
                        print("📸 截图已保存")
                    else:
                        print("❌ 未找到English选项")
                else:
                    print("❌ 重新点击小地球后菜单仍未出现")
                    page.screenshot(path="debug_final.png")
            except Exception as e:
                print(f"菜单处理出错: {e}")
                page.screenshot(path="debug_final.png")
            
            # 等待用户观察
            print("\n语言切换操作完成，按Enter键继续...")
            input()
            
        except Exception as e:
            print(f"执行出错: {e}")
            page.screenshot(path="debug_error.png")
        finally:
            browser.close()

if __name__ == "__main__":
    main() 
from playwright.sync_api import sync_playwright
import time

class PricelessBrowser:
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.page = None
        
    def start_browser(self, headless=False):
        """启动浏览器"""
        try:
            self.playwright = sync_playwright().start()
            
            # 简单启动浏览器，不做任何特征隐藏
            self.browser = self.playwright.chromium.launch(headless=headless)
            
            # 创建新页面，不设置特殊配置
            self.page = self.browser.new_page()
            
            print("浏览器启动成功")
            return True
            
        except Exception as e:
            print(f"启动浏览器失败: {e}")
            return False
    
    def goto_priceless_page(self):
        """访问Priceless页面"""
        try:
            print("正在访问Priceless页面...")
            url = "https://www.priceless.com/filter/options/contentType/1"
            
            # 访问页面
            response = self.page.goto(url, wait_until='networkidle', timeout=30000)
            
            if response.status == 200:
                print("页面加载成功")
                return True
            else:
                print(f"页面加载失败，状态码: {response.status}")
                return False
                
        except Exception as e:
            print(f"访问页面失败: {e}")
            return False
    
    def switch_to_english(self):
        """切换语言到English - 三步操作"""
        try:
            print("开始切换语言到English...")
            
            # 等待页面完全加载
            time.sleep(3)
            
            # 第零步：处理cookie同意弹窗
            print("第零步：处理cookie同意弹窗...")
            
            # 查找cookie同意按钮的选择器
            cookie_selectors = [
                # 常见的cookie同意按钮选择器
                'button:has-text("Accept")',
                'button:has-text("Accept All")',
                'button:has-text("同意")',
                'button:has-text("接受")',
                'button:has-text("Accept Cookies")',
                'button:has-text("Accept All Cookies")',
                '[data-testid="accept-cookies"]',
                '[data-testid="cookie-accept"]',
                '.cookie-accept',
                '.accept-cookies',
                '#accept-cookies',
                '#cookie-accept',
                'button[class*="cookie"][class*="accept"]',
                'button[id*="cookie"][id*="accept"]',
                '.cookie-banner button',
                '.cookie-notice button',
                '[class*="cookie"] button:has-text("Accept")',
                '[class*="cookie"] button:has-text("同意")',
                # OneTrust相关选择器（常见的cookie管理工具）
                '#onetrust-accept-btn-handler',
                '.onetrust-accept-btn-handler',
                '[data-module-name="cookie-banner"] button',
                # 通用选择器
                'button[aria-label*="accept"]',
                'button[aria-label*="cookie"]',
            ]
            
            cookie_handled = False
            for selector in cookie_selectors:
                try:
                    print(f"尝试cookie按钮选择器: {selector}")
                    cookie_button = self.page.locator(selector).first
                    
                    if cookie_button.count() > 0 and cookie_button.is_visible():
                        print(f"✅ 找到cookie同意按钮: {selector}")
                        try:
                            cookie_button.scroll_into_view_if_needed()
                            time.sleep(1)
                            cookie_button.click()
                            print("cookie同意按钮点击成功")
                            time.sleep(2)
                            cookie_handled = True
                            break
                        except Exception as e:
                            print(f"点击cookie按钮失败: {e}")
                            continue
                    else:
                        print(f"cookie按钮不可见或不存在: {selector}")
                        
                except Exception as e:
                    print(f"cookie按钮选择器 {selector} 出错: {e}")
                    continue
            
            if cookie_handled:
                print("✅ cookie弹窗已处理")
                time.sleep(2)  # 等待弹窗消失
            else:
                print("ℹ️ 未找到cookie弹窗或已处理")
            
            # 第一步：点击小地球图标
            print("第一步：查找并点击小地球图标...")
            
            # 查找小地球图标的选择器
            globe_selectors = [
                # 可能的小地球图标选择器
                '.globe-icon',
                '.language-globe',
                '[class*="globe"]',
                '[class*="language-icon"]',
                '.header-language-icon',
                'button[class*="globe"]',
                'a[class*="globe"]',
                # 通过图片src查找
                'img[src*="globe"]',
                'img[src*="language"]',
                'img[alt*="language"]',
                'img[alt*="globe"]',
                # 通过父容器查找
                '.language-selector',
                '.header-language',
                '[data-testid*="language"]',
                '[data-testid*="globe"]',
                # 通用选择器
                'button:has(img[src*="globe"])',
                'a:has(img[src*="globe"])',
                'div:has(img[src*="globe"])',
            ]
            
            globe_button = None
            for selector in globe_selectors:
                try:
                    print(f"尝试地球图标选择器: {selector}")
                    element = self.page.locator(selector).first
                    
                    if element.count() > 0 and element.is_visible():
                        print(f"✅ 找到小地球图标: {selector}")
                        globe_button = element
                        break
                    else:
                        print(f"地球图标不可见或不存在: {selector}")
                        
                except Exception as e:
                    print(f"地球图标选择器 {selector} 出错: {e}")
                    continue
            
            if not globe_button:
                print("❌ 未找到小地球图标")
                self.page.screenshot(path="debug_no_globe.png")
                return False
            
            # 点击小地球图标
            print("点击小地球图标...")
            try:
                globe_button.scroll_into_view_if_needed()
                time.sleep(1)
                globe_button.click()
                print("小地球图标点击成功")
                time.sleep(2)
            except Exception as e:
                print(f"点击小地球图标失败: {e}")
                return False
            
            # 第二步：点击语言选择按钮
            print("第二步：查找并点击语言选择按钮...")
            
            # 语言选择按钮的选择器
            language_button_selectors = [
                'button.current-language',
                '.current-language',
                'button:has-text("语言")',
                'button:has-text("简体中文")',
                '.sg-inline-middle.sg-f-bdy.io-button',
                '#lang-popup button',
                '#lang-popup-box button',
                'button >> text="语言"',
                'button >> text="简体中文"',
            ]
            
            language_button = None
            for selector in language_button_selectors:
                try:
                    print(f"尝试语言按钮选择器: {selector}")
                    element = self.page.locator(selector).first
                    
                    if element.count() > 0 and element.is_visible():
                        print(f"✅ 找到语言选择按钮: {selector}")
                        language_button = element
                        break
                    else:
                        print(f"语言按钮不可见或不存在: {selector}")
                        
                except Exception as e:
                    print(f"语言按钮选择器 {selector} 出错: {e}")
                    continue
            
            if not language_button:
                print("❌ 未找到语言选择按钮")
                self.page.screenshot(path="debug_no_language_button.png")
                return False
            
            # 点击语言选择按钮
            print("点击语言选择按钮...")
            try:
                # 先滚动到元素位置
                language_button.scroll_into_view_if_needed()
                time.sleep(1)
                
                # 使用更温和的点击方式
                print("尝试悬停到语言按钮...")
                language_button.hover()
                time.sleep(1)
                
                print("点击语言选择按钮...")
                # 尝试不同的点击方式
                try:
                    # 方式1：普通点击
                    language_button.click()
                    print("普通点击完成")
                except:
                    try:
                        # 方式2：强制点击
                        language_button.click(force=True)
                        print("强制点击完成")
                    except:
                        # 方式3：JavaScript点击
                        self.page.evaluate('arguments[0].click()', language_button.element_handle())
                        print("JavaScript点击完成")
                
                print("语言选择按钮点击成功，等待下拉框出现...")
                time.sleep(3)  # 增加等待时间
                
            except Exception as e:
                print(f"点击语言选择按钮失败: {e}")
                return False
            
            # 等待语言选择菜单出现
            print("等待语言选择菜单出现...")
            try:
                # 尝试多种方式等待菜单出现
                menu_selectors = [
                    '#language-scroll',
                    '.languages',
                    'ul#language-scroll',
                    '.languages-selector-item',
                    '#lang-popup',
                    '#lang-popup-box'
                ]
                
                menu_appeared = False
                for selector in menu_selectors:
                    try:
                        print(f"等待菜单选择器: {selector}")
                        self.page.wait_for_selector(selector, state='visible', timeout=5000)
                        print(f"✅ 语言菜单出现: {selector}")
                        menu_appeared = True
                        break
                    except:
                        print(f"菜单选择器 {selector} 未出现")
                        continue
                
                if not menu_appeared:
                    print("❌ 语言选择菜单未出现，可能已退回到小地球状态...")
                    print("尝试重新点击小地球图标...")
                    
                    # 重新查找并点击小地球图标
                    try:
                        # 等待一下让页面稳定
                        time.sleep(2)
                        
                        # 重新查找小地球图标
                        globe_button_retry = None
                        for selector in ['img[src*="globe"]', 'button:has(img[src*="globe"])', 'a:has(img[src*="globe"])']:
                            try:
                                element = self.page.locator(selector).first
                                if element.count() > 0 and element.is_visible():
                                    print(f"✅ 重新找到小地球图标: {selector}")
                                    globe_button_retry = element
                                    break
                            except:
                                continue
                        
                        if not globe_button_retry:
                            print("❌ 无法重新找到小地球图标")
                            self.page.screenshot(path="debug_no_globe_retry.png")
                            return False
                        
                        # 重新点击小地球图标
                        print("重新点击小地球图标...")
                        globe_button_retry.scroll_into_view_if_needed()
                        time.sleep(1)
                        globe_button_retry.click()
                        print("小地球图标重新点击成功")
                        time.sleep(2)
                        
                        # 重新查找并点击语言选择按钮
                        print("重新查找语言选择按钮...")
                        language_button_retry = None
                        for selector in ['button.current-language', '.current-language', 'button:has-text("语言")', 'button:has-text("简体中文")']:
                            try:
                                element = self.page.locator(selector).first
                                if element.count() > 0 and element.is_visible():
                                    print(f"✅ 重新找到语言选择按钮: {selector}")
                                    language_button_retry = element
                                    break
                            except:
                                continue
                        
                        if not language_button_retry:
                            print("❌ 无法重新找到语言选择按钮")
                            self.page.screenshot(path="debug_no_language_retry.png")
                            return False
                        
                        # 重新点击语言选择按钮
                        print("重新点击语言选择按钮...")
                        language_button_retry.scroll_into_view_if_needed()
                        time.sleep(1)
                        language_button_retry.hover()
                        time.sleep(1)
                        language_button_retry.click()
                        print("语言选择按钮重新点击成功")
                        time.sleep(3)
                        
                        # 再次等待菜单出现
                        print("再次等待语言选择菜单...")
                        self.page.wait_for_selector('#language-scroll', state='visible', timeout=10000)
                        print("✅ 重新操作后语言菜单出现")
                        
                    except Exception as retry_e:
                        print(f"❌ 重新操作失败: {retry_e}")
                        self.page.screenshot(path="debug_retry_failed.png")
                        return False
                    
            except Exception as e:
                print(f"等待语言菜单失败: {e}")
                self.page.screenshot(path="debug_no_menu.png")
                return False
            
            # 第三步：点击English选项
            print("第三步：查找并点击English选项...")
            
            # English选项的选择器
            english_selectors = [
                'button[lang-id="1"]',
                'button[data-lang-tag="en-US"]',
                'button:has-text("English")',
                '.languages-selector-item:has-text("English")',
                '[di-id="Language English"]',
                'button.languages-selector-item:has-text("English")',
                'li:has-text("English") button',
                '#language-scroll button:has-text("English")',
                'button >> text="English"',
            ]
            
            english_option = None
            for selector in english_selectors:
                try:
                    print(f"尝试English选择器: {selector}")
                    element = self.page.locator(selector).first
                    
                    if element.count() > 0 and element.is_visible():
                        print(f"✅ 找到English选项: {selector}")
                        english_option = element
                        break
                    else:
                        print(f"English选项不可见或不存在: {selector}")
                        
                except Exception as e:
                    print(f"English选择器 {selector} 出错: {e}")
                    continue
            
            if not english_option:
                print("❌ 未找到English选项")
                self.page.screenshot(path="debug_no_english.png")
                return False
            
            # 点击English选项
            print("点击English选项...")
            try:
                english_option.scroll_into_view_if_needed()
                time.sleep(1)
                english_option.click()
                print("English选项点击成功")
            except Exception as e:
                print(f"点击English选项失败: {e}")
                return False
            
            # 等待页面切换语言
            print("等待语言切换完成...")
            time.sleep(5)
            
            # 验证语言是否切换成功
            try:
                html_lang = self.page.locator('html').get_attribute('lang')
                print(f"切换后页面语言属性: {html_lang}")
                
                # 检查页面是否包含英文内容
                english_indicators = [
                    'text=Results',
                    'text=Search',
                    'text=Filter',
                    'text=English',
                    'text=Experience',
                    'text=Category'
                ]
                
                found_english = False
                for indicator in english_indicators:
                    try:
                        if self.page.locator(indicator).first.is_visible():
                            print(f"✅ 找到英文内容: {indicator}")
                            found_english = True
                            break
                    except:
                        continue
                
                if found_english or html_lang == 'en-US':
                    print("✅ 语言切换成功！页面已切换到English")
                    return True
                else:
                    print("❌ 语言切换可能未成功，未找到明确的英文内容")
                    return False
                    
            except Exception as e:
                print(f"验证语言切换时出错: {e}")
                return False
                
        except Exception as e:
            print(f"切换语言失败: {e}")
            return False
    
    def take_screenshot(self, filename="priceless_english.png"):
        """截图保存"""
        try:
            self.page.screenshot(path=filename, full_page=True)
            print(f"截图已保存: {filename}")
        except Exception as e:
            print(f"截图失败: {e}")
    
    def get_page_info(self):
        """获取页面信息"""
        try:
            title = self.page.title()
            url = self.page.url
            print(f"页面标题: {title}")
            print(f"当前URL: {url}")
            
            # 检查页面语言
            html_lang = self.page.locator('html').get_attribute('lang')
            print(f"页面语言属性: {html_lang}")
            
            return {
                'title': title,
                'url': url,
                'lang': html_lang
            }
        except Exception as e:
            print(f"获取页面信息失败: {e}")
            return None
    
    def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                self.page.close()
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            print("浏览器已关闭")
        except Exception as e:
            print(f"关闭浏览器失败: {e}")

def main():
    """主函数"""
    browser = PricelessBrowser()
    
    try:
        # 1. 启动浏览器
        if not browser.start_browser(headless=False):
            return
        
        # 2. 访问Priceless页面
        if not browser.goto_priceless_page():
            return
        
        # 3. 获取初始页面信息
        print("\n=== 初始页面信息 ===")
        browser.get_page_info()
        
        # 4. 切换语言到English（三步操作）
        print("\n=== 开始切换语言（三步操作）===")
        if browser.switch_to_english():
            print("✅ 语言切换成功")
        else:
            print("❌ 语言切换失败")
        
        # 5. 获取切换后的页面信息
        print("\n=== 切换后页面信息 ===")
        browser.get_page_info()
        
        # 6. 截图保存
        browser.take_screenshot()
        
        # 7. 停止等待用户观察
        print("\n语言切换操作完成，按Enter键继续...")
        input()
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"执行过程中出错: {e}")
    finally:
        # 8. 关闭浏览器
        browser.close_browser()

if __name__ == "__main__":
    main() 
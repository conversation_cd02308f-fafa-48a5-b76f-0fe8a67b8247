<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史任务与结果 - iCrawler</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">历史任务与结果</h2>
            <button id="clearHistory" class="btn btn-danger btn-sm ms-2">
                <i class="bi bi-trash"></i> 清空历史
            </button>
        </div>
        <div class="mb-3 text-end">
            <a href="/" class="btn btn-outline-primary"><i class="bi bi-arrow-left"></i> 返回首页</a>
        </div>
        <ul class="nav nav-tabs" id="historyTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="task-tab" data-bs-toggle="tab" data-bs-target="#task" type="button" role="tab">历史任务</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab">历史结果</button>
            </li>
        </ul>
        <div class="tab-content mt-3">
            <div class="tab-pane fade show active" id="task" role="tabpanel">
                {% if history and history|length > 0 %}
                <div class="accordion" id="taskAccordion">
                    {% for item in history|reverse %}
                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header" id="taskHeading{{ loop.index }}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#taskCollapse{{ loop.index }}" aria-expanded="false" aria-controls="taskCollapse{{ loop.index }}">
                                <span class="me-2 text-primary fw-bold">任务时间：</span>{{ item.time }}
                            </button>
                        </h2>
                        <div id="taskCollapse{{ loop.index }}" class="accordion-collapse collapse" aria-labelledby="taskHeading{{ loop.index }}" data-bs-parent="#taskAccordion">
                            <div class="accordion-body">
                                <div class="mb-2">
                                    <span class="fw-bold">任务描述：</span>
                                    {% if item.prompt is mapping %}
                                        <ul class="mb-0">
                                            <li><span class="fw-bold">目标网址：</span>{{ item.prompt.url or '' }}</li>
                                            <li><span class="fw-bold">任务类型：</span>{{ item.prompt.type or '' }}</li>
                                            <li><span class="fw-bold">数据要求：</span>{{ item.prompt.requirements or '' }}</li>
                                            <li><span class="fw-bold">数据保存格式：</span>{{ item.prompt.format or '' }}</li>
                                            {% if item.prompt.pagination %}<li><span class="fw-bold">翻页：</span>{{ item.prompt.pagination }}</li>{% endif %}
                                            {% if item.prompt.additional %}<li><span class="fw-bold">其他要求：</span>{{ item.prompt.additional }}</li>{% endif %}
                                        </ul>
                                    {% else %}
                                        {{ item.prompt }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info text-center">暂无历史任务记录。</div>
                {% endif %}
            </div>
            <div class="tab-pane fade" id="result" role="tabpanel">
                {% if history and history|length > 0 %}
                <div class="accordion" id="resultAccordion">
                    {% for item in history|reverse %}
                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header" id="resultHeading{{ loop.index }}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#resultCollapse{{ loop.index }}" aria-expanded="false" aria-controls="resultCollapse{{ loop.index }}">
                                <span class="me-2 text-success fw-bold">结果时间：</span>{{ item.time }}
                            </button>
                        </h2>
                        <div id="resultCollapse{{ loop.index }}" class="accordion-collapse collapse" aria-labelledby="resultHeading{{ loop.index }}" data-bs-parent="#resultAccordion">
                            <div class="accordion-body">
                                <div class="mb-2">
                                    <span class="fw-bold">任务状态：</span>
                                    <span class="badge bg-{{ 'success' if item.status == '已完成' else 'secondary' }}">{{ item.status or '未知' }}</span>
                                </div>
                                {% if item.code_file_path %}
                                <div class="mb-2">
                                    <span class="fw-bold">生成代码文件：</span>
                                    <a href="/download?file={{ item.code_file_path }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-download"></i> 下载代码
                                    </a>
                                </div>
                                {% endif %}
                                {% if item.file_path %}
                                <div class="mb-2">
                                    <span class="fw-bold">生成结果文件：</span>
                                    <a href="/download?file={{ item.file_path }}" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-download"></i> 下载结果
                                    </a>
                                </div>
                                {% endif %}
                                <div class="mb-2">
                                    <span class="fw-bold">任务描述：</span>
                                    {% if item.prompt is mapping %}
                                        <ul class="mb-0">
                                            <li><span class="fw-bold">目标网址：</span>{{ item.prompt.url or '' }}</li>
                                            <li><span class="fw-bold">任务类型：</span>{{ item.prompt.type or '' }}</li>
                                            <li><span class="fw-bold">数据要求：</span>{{ item.prompt.requirements or '' }}</li>
                                            <li><span class="fw-bold">数据保存格式：</span>{{ item.prompt.format or '' }}</li>
                                            {% if item.prompt.pagination %}<li><span class="fw-bold">翻页：</span>{{ item.prompt.pagination }}</li>{% endif %}
                                            {% if item.prompt.additional %}<li><span class="fw-bold">其他要求：</span>{{ item.prompt.additional }}</li>{% endif %}
                                        </ul>
                                    {% else %}
                                        {{ item.prompt }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info text-center">暂无历史结果记录。</div>
                {% endif %}
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.getElementById('clearHistory').onclick = function() {
    if (confirm('确定要清空所有历史记录吗？')) {
        fetch('/clear_history', {method: 'POST'})
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('清空失败：' + (data.message || '未知错误'));
                }
            });
    }
};
</script>
</body>
</html> 